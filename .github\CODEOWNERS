# Global code owners - these users will be requested for review on all PRs
* @your-username

# Trading engine and core functionality
paper_trader.py @your-username
trading_signals.py @your-username
risk_manager.py @your-username
portfolio_manager.py @your-username

# API and data handling
bybit_api.py @your-username
screener.py @your-username
technical_analysis.py @your-username

# Dashboard and UI
trading_dashboard.py @your-username
templates/ @your-username
static/ @your-username

# Configuration and settings
config.py @your-username
*.env @your-username

# Tests - require review for test changes
tests/ @your-username
test_*.py @your-username

# Documentation
*.md @your-username
docs/ @your-username

# CI/CD and workflows
.github/ @your-username
requirements.txt @your-username

# Git and project configuration
.gitignore @your-username
GIT_WORKFLOW.md @your-username
