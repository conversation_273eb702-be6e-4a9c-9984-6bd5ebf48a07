name: Continuous Integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', '3.11']

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        pip install pytest pytest-cov flake8 black isort
    
    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Check code formatting with black
      run: |
        black --check --diff .
    
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .
    
    - name: Run integration tests
      run: |
        python tests/test_integration_comprehensive.py
    
    - name: Run performance tests
      run: |
        python tests/test_performance.py
    
    - name: Run existing tests
      run: |
        if [ -f test_trading_bot.py ]; then python test_trading_bot.py; fi
        if [ -f test_portfolio_accounting.py ]; then python test_portfolio_accounting.py; fi
        if [ -f test_stop_loss_display.py ]; then python test_stop_loss_display.py; fi
    
    - name: Run comprehensive test suite
      run: |
        python tests/run_tests.py --output test_report_ci.json
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: test_report_ci.json

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run bandit security linter
      run: |
        bandit -r . -f json -o bandit-report.json || true
    
    - name: Check dependencies for known security vulnerabilities
      run: |
        if [ -f requirements.txt ]; then safety check -r requirements.txt; fi
    
    - name: Upload security results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-results
        path: bandit-report.json

  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install code quality tools
      run: |
        python -m pip install --upgrade pip
        pip install pylint mypy
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run pylint
      run: |
        pylint --output-format=json --reports=no --score=no . > pylint-report.json || true
    
    - name: Run mypy type checking
      run: |
        mypy . --ignore-missing-imports --json-report mypy-report || true
    
    - name: Upload code quality results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: code-quality-results
        path: |
          pylint-report.json
          mypy-report/

  build-status:
    runs-on: ubuntu-latest
    needs: [test, security, code-quality]
    if: always()
    steps:
    - name: Check build status
      run: |
        if [[ "${{ needs.test.result }}" == "success" && "${{ needs.security.result }}" == "success" && "${{ needs.code-quality.result }}" == "success" ]]; then
          echo "✅ All checks passed!"
          exit 0
        else
          echo "❌ Some checks failed:"
          echo "Tests: ${{ needs.test.result }}"
          echo "Security: ${{ needs.security.result }}"
          echo "Code Quality: ${{ needs.code-quality.result }}"
          exit 1
        fi
