# Git Workflow for Trading System Development

## Overview
This document defines the Git workflow to be followed for all code changes, ensuring proper version control, traceability, and collaboration. **Feature branches never directly impact main** - all changes go through pull requests and code review.

## Branching Strategy

### Branch Protection
- **main branch** is protected and requires pull requests
- **No direct commits** to main branch allowed
- **All changes** must go through feature branches and pull requests
- **Code review required** before merging to main

### Branch Types
- **main**: Production-ready code, always stable
- **feature/**: New features and enhancements
- **fix/**: Bug fixes and hotfixes
- **test/**: Test additions and improvements
- **docs/**: Documentation updates
- **refactor/**: Code refactoring without functional changes

## Standard Development Workflow

### 1. Pre-Development Setup
```bash
# Check current status and ensure on main
git status
git checkout main

# Pull latest changes from remote
git pull origin main

# Create feature branch from main
git checkout -b feature/descriptive-name

# Verify you're on the feature branch
git branch
```

### 2. Development Process
- Make logical, focused changes
- Test changes thoroughly on feature branch
- Commit frequently with clear messages
- Keep commits atomic (one logical change per commit)
- **Never merge directly to main**

### 3. Commit Standards

#### Commit Message Format
```
type(scope): brief description

Optional longer description explaining what and why.

- Additional bullet points if needed
- Reference issues: Fixes #123
```

#### Commit Types
- **feat**: New feature or enhancement
- **fix**: Bug fix
- **docs**: Documentation changes
- **test**: Adding or updating tests
- **refactor**: Code refactoring without functional changes
- **style**: Code style/formatting changes
- **perf**: Performance improvements
- **chore**: Maintenance tasks, dependency updates

#### Scope Examples
- **dashboard**: Dashboard-related changes
- **trading**: Trading engine modifications
- **risk**: Risk management updates
- **api**: API integration changes
- **config**: Configuration updates
- **portfolio**: Portfolio management changes

### 4. Example Commit Messages
```bash
# Good examples
git commit -m "feat(dashboard): add stop loss column to position display"
git commit -m "fix(portfolio): correct balance calculation in position opening"
git commit -m "test(accounting): add comprehensive portfolio accounting tests"
git commit -m "docs(readme): update installation instructions"
git commit -m "refactor(risk): simplify stop loss calculation logic"

# Multi-line example
git commit -m "fix(portfolio): correct portfolio accounting logic

- Position opening now deducts full trade amount
- Position closing returns correct trade value
- Total portfolio value calculation fixed
- Eliminates artificial portfolio inflation

Fixes #45"
```

### 5. Branch Naming Conventions
- **Features**: `feature/add-stop-loss-display`
- **Bug Fixes**: `fix/portfolio-accounting-error`
- **Tests**: `test/portfolio-accounting-tests`
- **Documentation**: `docs/update-api-documentation`
- **Refactoring**: `refactor/simplify-risk-manager`

### 6. Development Workflow Steps

#### For New Features
```bash
# 1. Create feature branch from main
git checkout main
git pull origin main
git checkout -b feature/new-feature-name

# 2. Make changes and test
# ... development work ...

# 3. Stage and commit changes
git add .
git status  # Review what's being committed
git commit -m "feat(scope): description"

# 4. Continue development with additional commits
git add specific-files
git commit -m "test(scope): add tests for new feature"

# 5. Push feature branch to remote
git push origin feature/new-feature-name

# 6. Create Pull Request (DO NOT MERGE DIRECTLY)
# - Go to GitHub/GitLab and create pull request
# - Request code review from team members
# - Ensure all tests pass in CI/CD
# - Address any review feedback

# 7. After PR approval and merge, clean up locally
git checkout main
git pull origin main  # Get the merged changes
git branch -d feature/new-feature-name  # Delete local feature branch
git push origin --delete feature/new-feature-name  # Delete remote feature branch
```

#### For Bug Fixes
```bash
# 1. Create fix branch from main
git checkout main
git pull origin main
git checkout -b fix/issue-description

# 2. Make fix and test
# ... fix implementation ...

# 3. Commit fix
git add .
git commit -m "fix(scope): resolve issue description

- Specific change made
- Why this fixes the issue
- Any side effects or considerations"

# 4. Add tests if needed
git add test-files
git commit -m "test(scope): add tests for bug fix"

# 5. Push and create Pull Request
git push origin fix/issue-description
# Create PR through GitHub/GitLab interface

# 6. After PR approval and merge, clean up
git checkout main
git pull origin main
git branch -d fix/issue-description
git push origin --delete fix/issue-description
```

### 7. File Organization for Commits

#### Logical Grouping
- **Code changes**: Group related functionality
- **Tests**: Separate commit for test additions
- **Documentation**: Separate commit for docs
- **Configuration**: Separate commit for config changes

#### Example Sequence
```bash
# 1. Core functionality
git add trading_dashboard.py paper_trader.py
git commit -m "feat(portfolio): implement corrected accounting logic"

# 2. Template updates
git add templates/trading_dashboard.html
git commit -m "feat(dashboard): add stop loss column to web interface"

# 3. Tests
git add test_portfolio_accounting.py test_dashboard_with_fix.py
git commit -m "test(portfolio): add comprehensive accounting tests"

# 4. Documentation
git add PORTFOLIO_ACCOUNTING_FIX.md
git commit -m "docs(portfolio): document accounting fix and improvements"
```

### 8. Pull Request Workflow

#### Creating Pull Requests
```bash
# After pushing feature branch
git push origin feature/branch-name

# Create PR through web interface with:
# - Clear title following commit message format
# - Detailed description of changes
# - Link to related issues
# - Screenshots/demos if applicable
# - Checklist of completed items
```

#### PR Template Example
```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Manual testing completed

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No merge conflicts
```

#### Code Review Process
- **Reviewer assignment**: Assign appropriate team members
- **Review criteria**: Code quality, test coverage, documentation
- **Feedback addressing**: Make requested changes in new commits
- **Approval required**: At least one approval before merge
- **CI/CD checks**: All automated tests must pass

### 9. Pre-Commit Checklist
- [ ] Code changes tested and working on feature branch
- [ ] Tests added/updated for new functionality
- [ ] Documentation updated if needed
- [ ] No debug code or temporary files included
- [ ] Commit message follows format standards
- [ ] Changes are logically grouped
- [ ] Feature branch is up to date with main

### 10. Special Situations

#### Emergency Hotfixes
```bash
# For critical production fixes - STILL USE PR PROCESS
git checkout main
git pull origin main
git checkout -b hotfix/critical-issue

# Make minimal fix
git commit -m "fix(critical): resolve production issue"
git push origin hotfix/critical-issue

# Create URGENT PR with expedited review
# After approval and merge:
git checkout main
git pull origin main
git branch -d hotfix/critical-issue
```

#### Large Refactoring
```bash
# Break into smaller commits
git add file1.py
git commit -m "refactor(module1): extract common functions"

git add file2.py
git commit -m "refactor(module2): simplify class structure"

git add tests/
git commit -m "test(refactor): update tests for refactored code"
```

### 11. Branch Protection and Repository Settings

#### Recommended Branch Protection Rules
```yaml
# GitHub branch protection settings for main branch
protection_rules:
  required_status_checks:
    strict: true
    contexts:
      - "ci/tests"
      - "ci/lint"
  enforce_admins: true
  required_pull_request_reviews:
    required_approving_review_count: 1
    dismiss_stale_reviews: true
    require_code_owner_reviews: false
  restrictions: null
  allow_force_pushes: false
  allow_deletions: false
```

#### Repository Configuration
- **Default branch**: main
- **Merge strategy**: Squash and merge (recommended)
- **Auto-delete head branches**: Enabled
- **Require branches to be up to date**: Enabled

### 12. Integration with Development Process

#### When Making Changes
1. **Always start with**: `git checkout main && git pull origin main`
2. **Create feature branch** for ALL changes (no exceptions)
3. **Commit early and often** with clear messages
4. **Test thoroughly** on feature branch before pushing
5. **Review changes** with `git diff` before committing
6. **Push feature branch** and create pull request
7. **Never merge directly** to main

#### Quality Gates (Enforced via PR)
- All tests must pass in CI/CD
- Code review approval required
- Documentation updated for user-facing changes
- No merge conflicts with main branch
- Branch must be up to date with main

## Tools and Commands Reference

### Useful Git Commands
```bash
# View commit history
git log --oneline --graph

# See what changed
git diff
git diff --staged

# Undo last commit (keep changes)
git reset --soft HEAD~1

# View branch status
git branch -v

# Clean up merged branches
git branch --merged | grep -v main | xargs git branch -d
```

### Integration with Development Tools
- Use `git status` frequently during development
- Leverage `git diff` to review changes before committing
- Use `git log` to understand change history
- Consider using Git hooks for automated testing

## Key Principles

### 🔒 **Main Branch Protection**
- **Never commit directly** to main branch
- **All changes** go through feature branches and pull requests
- **Code review required** for all changes
- **Automated testing** must pass before merge

### 🌿 **Feature Branch Workflow**
- **One feature per branch** - keep changes focused
- **Branch from main** - always start with latest code
- **Regular updates** - keep feature branch current with main
- **Clean history** - use meaningful commit messages

### 🔍 **Quality Assurance**
- **Test on feature branch** before creating PR
- **Self-review** all changes before requesting review
- **Address feedback** promptly and thoroughly
- **Maintain documentation** alongside code changes

## Conclusion

Following this Git workflow ensures:
- **Protected main branch**: Prevents accidental breaking changes
- **Code quality**: All changes reviewed before integration
- **Traceability**: Clear history of what changed and why
- **Collaboration**: Consistent practices for team development
- **Rollback capability**: Easy to revert problematic changes
- **Continuous integration**: Automated testing and validation

This workflow should be followed for all development work on the trading system, ensuring professional-grade version control practices with proper branch protection and code review processes.
