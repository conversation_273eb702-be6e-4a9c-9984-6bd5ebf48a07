# Cryptocurrency Paper Trading Bot

A comprehensive cryptocurrency paper trading bot that analyzes pinbar patterns and executes automated trades using a $1,000 virtual balance. The bot identifies reversal signals from pinbar patterns and manages positions with sophisticated risk management.

## 🚀 Features

### Core Trading Features
- **Automated Paper Trading**: Simulates real trading with $1,000 starting balance
- **Pinbar Pattern Detection**: Identifies bullish and bearish pinbar reversal patterns
- **Multi-Timeframe Support**: 30-minute, 4-hour, 12-hour, and daily candles
- **Risk Management**: Stop losses, take profits, and trailing stops
- **Position Management**: Maximum 5 positions (10% allocation each)
- **Real-time Monitoring**: Live portfolio tracking and performance analytics

### Pattern Analysis
- **Pinbar Detection**: Long wick patterns with body in upper/lower 1/3 of range
- **Volume Weighting**: Pattern strength adjusted by 24h trading volume
- **EMA Filtering**: Filters signals based on distance from 55-period EMA
- **Signal Confidence**: Scoring system based on pattern strength and market conditions

### Risk Management
- **Stop Loss**: Placed below/above pinbar wick with 1% buffer
- **Take Profit**: Multiple levels at 1.5:1, 2:1, and 3:1 risk-reward ratios
- **Trailing Stops**: Activated after 1:1 risk-reward ratio
- **Portfolio Limits**: Maximum daily loss (5%) and drawdown (15%) protection

### Dashboard Options
- **Web Dashboard**: Real-time web interface with portfolio monitoring
- **Console Dashboard**: Terminal-based interface for headless operation
- **Performance Analytics**: Win rate, P&L tracking, and trade history

## 📊 Trading Strategy

### Signal Generation
1. **Pattern Detection**: Scans completed candles for pinbar patterns
2. **Signal Filtering**: Applies volume, EMA distance, and strength filters
3. **Top 5 Selection**: Selects strongest signals for trading
4. **Position Entry**: Opens positions at market price with calculated stop loss

### Position Management
- **Entry**: Market orders at pattern close price
- **Position Size**: Risk-based sizing (max 2% risk per trade)
- **Stop Loss**: Below pinbar low (longs) or above pinbar high (shorts)
- **Take Profit**: Partial exits at 25%, 50%, and 75% of position
- **Trailing Stop**: Moves stop loss in favorable direction

### Risk Controls
- **Maximum Positions**: 5 concurrent positions
- **Position Allocation**: 10% of balance per position
- **Daily Loss Limit**: 5% of account balance
- **Maximum Drawdown**: 15% of peak balance
- **Commission**: 0.1% per trade with 0.05% slippage

## Pattern Detection Criteria

### Pinbars
- Wick length ≥ 2x body size
- Body positioned in upper/lower 1/3 of candle range
- **Bullish Pinbar**: Long lower wick, body in upper third → Long position
- **Bearish Pinbar**: Long upper wick, body in lower third → Short position

### Signal Filtering
- **Pattern Strength**: Minimum 2.0x for trading (vs 1.5x for screening)
- **EMA Distance**: Maximum 3% from 55-period EMA for trading
- **Volume Requirement**: Minimum $1M 24h volume
- **Top 5 Selection**: Only strongest patterns are traded

## 🛠️ Installation

1. **Clone Repository**:
   ```bash
   git clone <repository-url>
   cd "4hr trader"
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run Trading Bot**:
   ```bash
   # Web dashboard mode (default)
   python main_trader.py

   # Console mode
   python main_trader.py --console

   # Different timeframes
   python main_trader.py --timeframe 30m
   python main_trader.py --timeframe 12h
   ```

## 🖥️ Usage

### Web Dashboard Mode
```bash
# Default 4-hour timeframe
python main_trader.py

# Custom timeframe and port
python main_trader.py --timeframe 12h --port 8080
```

Access dashboard at: `http://localhost:5000`

### Console Mode
```bash
# Console-only mode (no web interface)
python main_trader.py --console

# Custom update interval
python main_trader.py --console --console-interval 60
```

### Batch Files (Windows)
- `start_trading_bot.bat` - Web dashboard mode
- `start_trading_bot_console.bat` - Console mode

## Timeframe Options

| Timeframe | Command | Update Frequency | Candle Check | Best For |
|-----------|---------|------------------|--------------|----------|
| **4-Hour** | `python main.py -t 4h` | 30 seconds | 1 minute | Intraday trading |
| **12-Hour** | `python main.py -t 12h` | 2 minutes | 5 minutes | Swing trading |
| **Daily** | `python main.py -t 1d` | 5 minutes | 10 minutes | Position trading |

## Dashboard Layout

### Current Candle Table
- Shows developing patterns in the current 4-hour candle
- Updates every 30 seconds
- Patterns may change as the candle develops
- **Sortable columns**: Click headers to sort by Pair, Pattern, Strength, Volume, or EMA Distance

### Previous Candle Table
- Shows confirmed patterns from the last completed 4-hour candle
- Updated when new candles close (every 4 hours)
- More reliable for trading decisions
- **Sortable columns**: Independent sorting from current table

### Column Details
- **Pair**: Trading pair symbol (clickable for Bybit trading)
- **Pattern**: Type of pattern detected (color-coded)
- **Strength**: Final strength showing base × volume multiplier
- **24h Volume**: USDT turnover with visual highlighting
- **EMA Distance**: Percentage distance from 55 EMA
- **Action**: Direct link to Bybit trading page

## 4-Hour Candle Schedule (UTC)
- 00:00 - 04:00
- 04:00 - 08:00
- 08:00 - 12:00
- 12:00 - 16:00
- 16:00 - 20:00
- 20:00 - 00:00

## Local Time Context
If your local time is 6 hours ahead of UTC:
- 4hr candle starts at 6pm local
- 4hr candle closes at 10pm local

## File Structure

```
├── main.py                 # Application entry point
├── bybit_api.py           # Bybit API client
├── technical_analysis.py  # Pattern detection algorithms
├── screener.py            # Main screening engine
├── dashboard.py           # Web dashboard
├── config.py              # Configuration settings
├── test_screener.py       # Test suite
├── check_status.py        # Status checker
├── start_screener.bat     # Windows startup script (4h default)
├── start_screener_12h.bat # Windows startup script (12h)
├── start_screener_1d.bat  # Windows startup script (1d)
├── templates/
│   └── dashboard.html     # Dashboard HTML template
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## Configuration

You can modify settings in `config.py`:

**Pattern Detection:**
- `pinbar_min_wick_ratio`: Minimum wick-to-body ratio for pinbars (default: 2.0x)
- `pinbar_body_position_threshold`: Body position threshold (default: 0.33)
- `long_wick_min_ratio`: Minimum wick-to-body ratio for long wicks (default: 1.5x)
- `max_ema_distance`: Maximum % distance from EMA (default: 5.0%)
- `min_pattern_strength`: Minimum pattern strength (default: 1.5x)

**Performance:**
- `max_workers`: Concurrent API requests (default: 10)
- `update_interval`: Current patterns update frequency (default: 30s)

## Logging

The application logs to both console and `screener.log` file. Check the log file for detailed information about API calls, pattern detection, and any errors.

## Troubleshooting

1. **No patterns detected**: This is normal - patterns are relatively rare
2. **API errors**: Check your internet connection and Bybit API status
3. **Dashboard not loading**: Ensure port 5000 is available

## Advanced Usage

### Command Line Examples
```bash
# Help and options
python main.py --help

# Different timeframes
python main.py --timeframe 4h --port 5000
python main.py --timeframe 12h --port 8080
python main.py --timeframe 1d --port 9000

# Run multiple instances (different ports)
python main.py -t 4h -p 5000 &   # 4h on port 5000
python main.py -t 12h -p 5001 &  # 12h on port 5001
python main.py -t 1d -p 5002 &   # 1d on port 5002
```

### Timeframe Characteristics
- **4-Hour**: Best for intraday trading, frequent updates, more patterns
- **12-Hour**: Good for swing trading, balanced update frequency
- **Daily**: Ideal for position trading, fewer but stronger patterns

## Disclaimer

This tool is for educational and informational purposes only. Always do your own research before making trading decisions. Cryptocurrency trading involves significant risk.

## Support

For issues or questions, check the log files for error messages and ensure all dependencies are properly installed.
