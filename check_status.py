"""
Quick status check for the cryptocurrency screener
"""
import requests
import json
from datetime import datetime

def check_screener_status():
    """Check if the screener is running and get current status"""
    try:
        # Try to connect to the dashboard API
        response = requests.get('http://localhost:5000/api/data', timeout=15)

        if response.status_code == 200:
            data = response.json()

            print("✅ Screener is RUNNING")
            print(f"📊 Dashboard: http://localhost:5000")
            print(f"🔄 Last Update: {data.get('last_update', 'Unknown')}")
            print(f"📈 Current Patterns: {len(data.get('current_patterns', []))}")
            print(f"✅ Previous Patterns: {len(data.get('previous_patterns', []))}")
            print(f"🎯 Total Pairs Monitored: {data.get('total_pairs', 'Unknown')}")

            # Show top 5 current patterns
            current = data.get('current_patterns', [])[:5]
            if current:
                print("\n🔥 Top Current Patterns:")
                for i, pattern in enumerate(current, 1):
                    print(f"  {i}. {pattern['symbol']} - {pattern['pattern_type']} ({pattern['strength']})")

            return True

        else:
            print(f"❌ Screener API returned status code: {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ Screener is NOT RUNNING")
        print("💡 Start it with: python main.py")
        return False

    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return False

def main():
    print("🚀 Cryptocurrency 4H Pattern Screener - Status Check")
    print("=" * 60)
    print(f"⏰ Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    is_running = check_screener_status()

    print("\n" + "=" * 60)
    if is_running:
        print("🎉 All systems operational!")
    else:
        print("⚠️  Screener needs to be started")

if __name__ == "__main__":
    main()
