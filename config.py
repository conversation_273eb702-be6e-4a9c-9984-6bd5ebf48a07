"""
Configuration settings for the Cryptocurrency 4H Pattern Screener
"""

# Pattern Detection Settings
PATTERN_CONFIG = {
    # Pinbar detection criteria
    'pinbar_min_wick_ratio': 2.0,          # Minimum wick-to-body ratio for pinbars
    'pinbar_body_position_threshold': 0.33, # Body must be in upper/lower 1/3 of range

    # Long wick detection criteria
    'long_wick_min_ratio': 1.5,            # Minimum wick-to-body ratio for long wicks

    # Filtering criteria
    'max_ema_distance': 5.0,               # Maximum % distance from 55 EMA to consider
    'min_pattern_strength': 1.5,           # Minimum pattern strength to display
}

# API Settings
API_CONFIG = {
    'max_workers': 10,                      # Number of concurrent API requests
    'request_timeout': 30,                  # Timeout for individual API requests (seconds)
    'historical_candles': 100,              # Number of historical candles to fetch for EMA
}

# Dashboard Settings
DASHBOARD_CONFIG = {
    'port': 5000,                          # Web dashboard port
    'update_interval': 30,                 # Current patterns update interval (seconds)
    'candle_check_interval': 60,           # Candle transition check interval (seconds)
}

# Logging Settings
LOGGING_CONFIG = {
    'level': 'INFO',                       # Logging level (DEBUG, INFO, WARNING, ERROR)
    'log_file': 'screener.log',           # Log file name
    'max_log_size': 10 * 1024 * 1024,    # Maximum log file size (10MB)
    'backup_count': 5,                     # Number of backup log files to keep
}

# Timeframe Settings
TIMEFRAME_CONFIG = {
    '30m': {
        'interval_minutes': 30,            # 30 minutes
        'bybit_interval': '30',            # Bybit API interval parameter
        'update_interval': 15,             # Update every 15 seconds
        'candle_check_interval': 30,       # Check for new candles every 30 seconds
        'display_name': '30-Minute',
        'short_name': '30M'
    },
    '4h': {
        'interval_minutes': 240,           # 4 hours in minutes
        'bybit_interval': '240',           # Bybit API interval parameter
        'update_interval': 30,             # Update every 30 seconds
        'candle_check_interval': 60,       # Check for new candles every minute
        'display_name': '4-Hour',
        'short_name': '4H'
    },
    '12h': {
        'interval_minutes': 720,           # 12 hours in minutes
        'bybit_interval': '720',           # Bybit API interval parameter
        'update_interval': 120,            # Update every 2 minutes
        'candle_check_interval': 300,      # Check for new candles every 5 minutes
        'display_name': '12-Hour',
        'short_name': '12H'
    },
    '1d': {
        'interval_minutes': 1440,          # 24 hours in minutes
        'bybit_interval': 'D',             # Bybit API interval parameter (D for daily)
        'update_interval': 300,            # Update every 5 minutes
        'candle_check_interval': 600,      # Check for new candles every 10 minutes
        'display_name': 'Daily',
        'short_name': '1D'
    }
}

# Time Settings
TIME_CONFIG = {
    'timezone': 'UTC',                     # Timezone for candle alignment
    'default_timeframe': '4h',             # Default timeframe if none specified
}

# Display Settings
DISPLAY_CONFIG = {
    'max_patterns_per_table': 50,          # Maximum patterns to show in each table
    'price_decimal_places': 4,             # Decimal places for price display
    'percentage_decimal_places': 1,        # Decimal places for percentage display
}

# Paper Trading Settings
TRADING_CONFIG = {
    'starting_balance': 1000.0,            # Starting balance in USD
    'max_positions': 5,                    # Maximum number of concurrent positions
    'position_size_percent': 10.0,         # Percentage of balance per position (10%)
    'max_total_allocation': 50.0,          # Maximum total allocation percentage (50%)
    'min_trade_amount': 10.0,              # Minimum trade amount in USD
    'commission_rate': 0.001,              # Trading commission rate (0.1%)
    'slippage_percent': 0.05,              # Slippage percentage (0.05%)
}

# Risk Management Settings
RISK_CONFIG = {
    'stop_loss_buffer_percent': 1.0,       # Buffer below/above wick for stop loss (1%)
    'take_profit_levels': [25, 50, 75],    # Take profit percentages (25%, 50%, 75% of position)
    'take_profit_ratios': [1.5, 2.0, 3.0], # Risk:reward ratios for take profit levels
    'trailing_stop_activation': 1.0,       # Activate trailing stop after 1:1 risk:reward
    'trailing_stop_distance': 0.5,         # Trailing stop distance as % of entry-to-target
    'max_risk_per_trade': 2.0,             # Maximum risk per trade as % of balance
    'max_daily_loss': 5.0,                 # Maximum daily loss as % of balance
    'max_drawdown': 15.0,                  # Maximum drawdown as % of balance
}

# Signal Generation Settings
SIGNAL_CONFIG = {
    'min_pattern_strength_for_trade': 2.0, # Minimum pattern strength to generate trade signal
    'max_ema_distance_for_trade': 3.0,     # Maximum EMA distance for trade signals
    'required_volume_24h': 1_000_000,      # Minimum 24h volume in USDT for trading
    'top_signals_count': 5,                # Number of top signals to trade
    'signal_timeframes': ['30m', '4h', '12h', '1d'], # Supported timeframes for signals
    'entry_scale_levels': 3,               # Number of scale-in entry levels
    'entry_scale_spacing': 0.5,            # Spacing between entry levels (% of wick size)
}
