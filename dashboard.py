"""
Web dashboard for cryptocurrency trading screener
"""
from flask import Flask, render_template, jsonify
import threading
import time
import schedule
from datetime import datetime, timezone
import logging

from screener import CryptoScreener

logger = logging.getLogger(__name__)

class Dashboard:
    def __init__(self, port: int = 5000, timeframe: str = '4h', timeframe_config: dict = None):
        self.app = Flask(__name__)
        self.timeframe = timeframe
        self.timeframe_config = timeframe_config or {
            'display_name': '4-Hour',
            'short_name': '4H',
            'update_interval': 30,
            'candle_check_interval': 60
        }
        self.screener = CryptoScreener(timeframe_config=timeframe_config)
        self.port = port
        self.running = False

        # Setup routes
        self.setup_routes()

        # Background scheduler
        self.scheduler_thread = None

    def setup_routes(self):
        """Setup Flask routes"""

        @self.app.route('/')
        def index():
            """Main dashboard page"""
            return render_template('dashboard.html',
                                 timeframe=self.timeframe,
                                 timeframe_config=self.timeframe_config)

        @self.app.route('/api/data')
        def get_data():
            """API endpoint for dashboard data"""
            try:
                data = self.screener.get_dashboard_data()
                # Add timeframe information to response
                data['timeframe'] = self.timeframe
                data['timeframe_config'] = self.timeframe_config
                return jsonify(data)
            except Exception as e:
                logger.error(f"Error getting dashboard data: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/refresh')
        def refresh_data():
            """API endpoint to manually refresh current patterns"""
            try:
                self.screener.update_current_patterns()
                return jsonify({'status': 'success', 'message': 'Data refreshed'})
            except Exception as e:
                logger.error(f"Error refreshing data: {e}")
                return jsonify({'error': str(e)}), 500

    def update_current_patterns_job(self):
        """Job to update current patterns"""
        logger.info("Updating current patterns...")
        self.screener.update_current_patterns()

    def update_previous_patterns_job(self):
        """Job to update previous patterns (when candle closes)"""
        logger.info("Updating previous patterns...")
        self.screener.update_previous_patterns()

    def check_candle_transition_job(self):
        """Job to check for candle transitions"""
        if self.screener.check_candle_transition():
            logger.info(f"New {self.timeframe_config['display_name'].lower()} candle detected - updating previous patterns")
            self.screener.update_previous_patterns()

    def run_scheduler(self):
        """Run the background scheduler"""
        # Schedule current pattern updates based on timeframe
        update_interval = self.timeframe_config['update_interval']
        schedule.every(update_interval).seconds.do(self.update_current_patterns_job)

        # Schedule candle transition checks based on timeframe
        check_interval = self.timeframe_config['candle_check_interval']
        schedule.every(check_interval).seconds.do(self.check_candle_transition_job)

        # Initial data load
        logger.info("Loading initial data...")
        self.screener.update_current_patterns()
        self.screener.update_previous_patterns()

        while self.running:
            schedule.run_pending()
            time.sleep(1)

    def start(self):
        """Start the dashboard"""
        self.running = True

        # Start background scheduler
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logger.info(f"Starting dashboard on port {self.port}")
        self.app.run(host='0.0.0.0', port=self.port, debug=False, threaded=True)

    def stop(self):
        """Stop the dashboard"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)


