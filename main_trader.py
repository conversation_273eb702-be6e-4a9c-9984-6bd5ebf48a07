"""
Main entry point for the Cryptocurrency Paper Trading Bot
"""
import logging
import sys
import signal
import argparse
import time
import threading
from datetime import datetime, timezone

from trading_dashboard import TradingDashboard
from config import TIMEFRAME_CONFIG, TIME_CONFIG

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def signal_handler(sig, frame):
    """Handle shutdown signals gracefully"""
    logger.info("Received shutdown signal. Stopping trading bot...")
    sys.exit(0)

def parse_arguments():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(
        description='Cryptocurrency Paper Trading Bot - Automated pinbar pattern trading',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_trader.py                     # Default 4-hour timeframe with web dashboard
  python main_trader.py --timeframe 4h      # 4-hour candles
  python main_trader.py --timeframe 12h     # 12-hour candles
  python main_trader.py --timeframe 1d      # Daily candles
  python main_trader.py --timeframe 30m     # 30-minute candles
  python main_trader.py -t 12h --console    # Console-only mode
  python main_trader.py --port 8080         # Custom port for web dashboard

Timeframes:
  30m  - 30-minute candles (updates every 15 seconds)
  4h   - 4-hour candles (updates every 30 seconds)
  12h  - 12-hour candles (updates every 2 minutes)
  1d   - Daily candles (updates every 5 minutes)

Features:
  - Analyzes pinbar patterns from previous completed candles
  - Executes paper trades with $1,000 starting balance
  - Risk management with stop losses and take profits
  - Real-time portfolio monitoring
  - Performance tracking and analytics
        """
    )

    parser.add_argument(
        '--timeframe', '-t',
        choices=['30m', '4h', '12h', '1d'],
        default=TIME_CONFIG['default_timeframe'],
        help='Timeframe for pattern analysis and trading (default: %(default)s)'
    )

    parser.add_argument(
        '--port', '-p',
        type=int,
        default=5000,
        help='Port for web dashboard (default: %(default)s)'
    )

    parser.add_argument(
        '--console', '-c',
        action='store_true',
        help='Run in console-only mode (no web dashboard)'
    )

    parser.add_argument(
        '--console-interval',
        type=int,
        default=30,
        help='Console update interval in seconds (default: %(default)s)'
    )

    return parser.parse_args()

def run_console_mode(timeframe: str, update_interval: int):
    """Run the bot in console-only mode"""
    timeframe_config = TIMEFRAME_CONFIG[timeframe]
    
    logger.info(f"Starting console mode for {timeframe_config['display_name']} timeframe")
    logger.info(f"Console updates every {update_interval} seconds")
    
    # Create dashboard instance but don't start web server
    dashboard = TradingDashboard(
        timeframe=timeframe,
        timeframe_config=timeframe_config
    )
    
    # Start the background scheduler
    dashboard.running = True
    dashboard.scheduler_thread = threading.Thread(target=dashboard.run_scheduler, daemon=True)
    dashboard.scheduler_thread.start()
    
    try:
        while True:
            # Clear screen (works on most terminals)
            print("\033[2J\033[H")
            
            # Print console dashboard
            dashboard.print_console_dashboard()
            
            # Wait for next update
            time.sleep(update_interval)
            
    except KeyboardInterrupt:
        logger.info("Console mode stopped by user")
    finally:
        dashboard.stop()

def run_web_mode(timeframe: str, port: int):
    """Run the bot with web dashboard"""
    timeframe_config = TIMEFRAME_CONFIG[timeframe]
    
    logger.info(f"Starting web dashboard mode for {timeframe_config['display_name']} timeframe")
    
    try:
        # Create and start dashboard with timeframe configuration
        dashboard = TradingDashboard(
            port=port,
            timeframe=timeframe,
            timeframe_config=timeframe_config
        )

        logger.info(f"Web dashboard will be available at: http://localhost:{port}")
        logger.info("Press Ctrl+C to stop the trading bot")

        # Start the dashboard (this will block)
        dashboard.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt. Shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        logger.info("Trading bot stopped")

def print_startup_info(timeframe: str, console_mode: bool, port: int = None):
    """Print startup information"""
    timeframe_config = TIMEFRAME_CONFIG[timeframe]
    
    print("\n" + "="*80)
    print("CRYPTOCURRENCY PAPER TRADING BOT")
    print("="*80)
    print(f"Timeframe: {timeframe_config['display_name']} ({timeframe})")
    print(f"Update Interval: {timeframe_config['update_interval']} seconds")
    print(f"Starting Balance: $1,000 USD")
    print(f"Max Positions: 5 (10% allocation each)")
    print(f"Strategy: Pinbar pattern reversal trading")
    
    if console_mode:
        print("Mode: Console Dashboard")
    else:
        print(f"Mode: Web Dashboard (Port {port})")
    
    print("\nFeatures:")
    print("  ✓ Real-time pinbar pattern detection")
    print("  ✓ Automated paper trading execution")
    print("  ✓ Risk management with stop losses")
    print("  ✓ Take profit levels and trailing stops")
    print("  ✓ Portfolio performance tracking")
    print("  ✓ Trade history and analytics")
    
    print("\nRisk Management:")
    print("  • Stop loss: Below/above pinbar wick with 1% buffer")
    print("  • Take profit: 1.5:1, 2:1, 3:1 risk-reward ratios")
    print("  • Trailing stop: Activated after 1:1 ratio")
    print("  • Max daily loss: 5% of balance")
    print("  • Max drawdown: 15% of balance")
    
    print("="*80)

def main():
    """Main function to start the cryptocurrency trading bot"""
    # Parse command-line arguments
    args = parse_arguments()

    # Validate timeframe
    if args.timeframe not in TIMEFRAME_CONFIG:
        logger.error(f"Invalid timeframe: {args.timeframe}")
        sys.exit(1)

    # Get timeframe configuration
    timeframe_config = TIMEFRAME_CONFIG[args.timeframe]

    # Print startup information
    print_startup_info(args.timeframe, args.console, args.port)

    # Setup signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info(f"Starting Cryptocurrency Paper Trading Bot")
    logger.info(f"Timeframe: {timeframe_config['display_name']} ({args.timeframe})")
    logger.info(f"Update interval: {timeframe_config['update_interval']} seconds")

    try:
        if args.console:
            run_console_mode(args.timeframe, args.console_interval)
        else:
            run_web_mode(args.timeframe, args.port)

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
