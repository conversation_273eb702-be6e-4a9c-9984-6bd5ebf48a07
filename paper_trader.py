"""
Paper Trading Engine for Cryptocurrency Trading Bot
Simulates real trading without using actual funds
"""
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

from config import TRADING_CONFIG, RISK_CONFIG

logger = logging.getLogger(__name__)

class PositionType(Enum):
    LONG = "long"
    SHORT = "short"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    PARTIAL = "partial"

@dataclass
class Order:
    """Represents a trading order"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    symbol: str = ""
    order_type: OrderType = OrderType.MARKET
    position_type: PositionType = PositionType.LONG
    quantity: float = 0.0
    price: float = 0.0
    stop_price: float = 0.0
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    filled_at: Optional[datetime] = None
    filled_price: float = 0.0
    filled_quantity: float = 0.0
    commission: float = 0.0

@dataclass
class Position:
    """Represents an open trading position"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    symbol: str = ""
    position_type: PositionType = PositionType.LONG
    entry_price: float = 0.0
    quantity: float = 0.0
    current_price: float = 0.0
    stop_loss: float = 0.0
    take_profit_levels: List[Tuple[float, float]] = field(default_factory=list)  # (price, quantity)
    trailing_stop: Optional[float] = None
    trailing_stop_activated: bool = False
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    commission_paid: float = 0.0
    opened_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    pattern_strength: float = 0.0
    pattern_type: str = ""
    entry_reason: str = ""
    risk_amount: float = 0.0
    
    def update_unrealized_pnl(self, current_price: float):
        """Update unrealized P&L based on current price"""
        self.current_price = current_price
        if self.position_type == PositionType.LONG:
            self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
        else:
            self.unrealized_pnl = (self.entry_price - current_price) * self.quantity
        
        # Subtract commission
        self.unrealized_pnl -= self.commission_paid

@dataclass
class Trade:
    """Represents a completed trade"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    symbol: str = ""
    position_type: PositionType = PositionType.LONG
    entry_price: float = 0.0
    exit_price: float = 0.0
    quantity: float = 0.0
    realized_pnl: float = 0.0
    commission: float = 0.0
    opened_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    closed_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    pattern_strength: float = 0.0
    pattern_type: str = ""
    exit_reason: str = ""
    hold_time_minutes: float = 0.0
    risk_reward_ratio: float = 0.0

class PaperTradingEngine:
    """Core paper trading engine"""
    
    def __init__(self, starting_balance: float = None):
        self.starting_balance = starting_balance or TRADING_CONFIG['starting_balance']
        self.current_balance = self.starting_balance
        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, Order] = {}
        self.completed_trades: List[Trade] = []
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_balance = self.starting_balance
        
        logger.info(f"Paper trading engine initialized with ${self.starting_balance:,.2f}")
    
    def get_available_balance(self) -> float:
        """Get available balance for new trades"""
        allocated_balance = sum(
            pos.entry_price * pos.quantity for pos in self.positions.values()
        )
        return self.current_balance - allocated_balance
    
    def get_position_allocation_percent(self) -> float:
        """Get current position allocation as percentage of balance"""
        allocated_balance = sum(
            pos.entry_price * pos.quantity for pos in self.positions.values()
        )
        return (allocated_balance / self.current_balance) * 100 if self.current_balance > 0 else 0
    
    def can_open_position(self, trade_amount: float) -> Tuple[bool, str]:
        """Check if we can open a new position"""
        # Check maximum positions
        if len(self.positions) >= TRADING_CONFIG['max_positions']:
            return False, f"Maximum positions ({TRADING_CONFIG['max_positions']}) reached"
        
        # Check available balance
        available = self.get_available_balance()
        if trade_amount > available:
            return False, f"Insufficient balance. Available: ${available:.2f}, Required: ${trade_amount:.2f}"
        
        # Check maximum allocation
        current_allocation = self.get_position_allocation_percent()
        position_allocation = (trade_amount / self.current_balance) * 100
        total_allocation = current_allocation + position_allocation
        
        if total_allocation > TRADING_CONFIG['max_total_allocation']:
            return False, f"Would exceed maximum allocation ({TRADING_CONFIG['max_total_allocation']}%)"
        
        # Check minimum trade amount
        if trade_amount < TRADING_CONFIG['min_trade_amount']:
            return False, f"Trade amount below minimum (${TRADING_CONFIG['min_trade_amount']})"
        
        return True, "OK"
    
    def calculate_position_size(self, entry_price: float, stop_loss: float) -> Tuple[float, float]:
        """Calculate position size based on risk management"""
        # Calculate risk per share
        if entry_price <= 0 or stop_loss <= 0:
            return 0.0, 0.0
        
        risk_per_share = abs(entry_price - stop_loss)
        
        # Calculate maximum risk amount (percentage of balance)
        max_risk_amount = self.current_balance * (RISK_CONFIG['max_risk_per_trade'] / 100)
        
        # Calculate position size based on risk
        if risk_per_share > 0:
            risk_based_quantity = max_risk_amount / risk_per_share
            risk_based_amount = risk_based_quantity * entry_price
        else:
            risk_based_quantity = 0.0
            risk_based_amount = 0.0
        
        # Calculate position size based on allocation percentage
        allocation_amount = self.current_balance * (TRADING_CONFIG['position_size_percent'] / 100)
        allocation_quantity = allocation_amount / entry_price if entry_price > 0 else 0.0
        
        # Use the smaller of the two approaches
        final_quantity = min(risk_based_quantity, allocation_quantity)
        final_amount = final_quantity * entry_price
        
        return final_quantity, final_amount
    
    def apply_slippage_and_commission(self, price: float, quantity: float, is_buy: bool) -> Tuple[float, float]:
        """Apply slippage and calculate commission"""
        # Apply slippage
        slippage_factor = TRADING_CONFIG['slippage_percent'] / 100
        if is_buy:
            adjusted_price = price * (1 + slippage_factor)
        else:
            adjusted_price = price * (1 - slippage_factor)
        
        # Calculate commission
        trade_value = adjusted_price * quantity
        commission = trade_value * TRADING_CONFIG['commission_rate']
        
        return adjusted_price, commission

    def open_position(self, symbol: str, position_type: PositionType, entry_price: float,
                     stop_loss: float, pattern_data: Dict) -> Tuple[bool, str, Optional[Position]]:
        """Open a new position"""
        try:
            # Calculate position size
            quantity, trade_amount = self.calculate_position_size(entry_price, stop_loss)

            if quantity <= 0:
                return False, "Invalid position size calculated", None

            # Check if we can open the position
            can_open, reason = self.can_open_position(trade_amount)
            if not can_open:
                return False, reason, None

            # Apply slippage and commission
            is_buy = position_type == PositionType.LONG
            adjusted_price, commission = self.apply_slippage_and_commission(entry_price, quantity, is_buy)

            # Create position
            position = Position(
                symbol=symbol,
                position_type=position_type,
                entry_price=adjusted_price,
                quantity=quantity,
                current_price=adjusted_price,
                stop_loss=stop_loss,
                commission_paid=commission,
                pattern_strength=pattern_data.get('pattern_strength', 0.0),
                pattern_type=pattern_data.get('pattern_type', ''),
                entry_reason=f"Pinbar pattern detected: {pattern_data.get('pattern_type', '')}",
                risk_amount=abs(adjusted_price - stop_loss) * quantity
            )

            # Calculate take profit levels
            position.take_profit_levels = self._calculate_take_profit_levels(
                position, pattern_data
            )

            # Store position
            self.positions[position.id] = position

            # Update balance (deduct full trade amount + commission)
            trade_amount = adjusted_price * quantity
            self.current_balance -= (trade_amount + commission)

            logger.info(f"Opened {position_type.value} position for {symbol}: "
                       f"${adjusted_price:.4f} x {quantity:.4f} = ${trade_amount:.2f}")

            return True, "Position opened successfully", position

        except Exception as e:
            logger.error(f"Error opening position for {symbol}: {e}")
            return False, str(e), None

    def close_position(self, position_id: str, exit_price: float, exit_reason: str = "Manual close") -> Tuple[bool, str, Optional[Trade]]:
        """Close an existing position"""
        try:
            if position_id not in self.positions:
                return False, "Position not found", None

            position = self.positions[position_id]

            # Apply slippage and commission for exit
            is_sell = position.position_type == PositionType.LONG
            adjusted_exit_price, exit_commission = self.apply_slippage_and_commission(
                exit_price, position.quantity, not is_sell
            )

            # Calculate realized P&L
            if position.position_type == PositionType.LONG:
                gross_pnl = (adjusted_exit_price - position.entry_price) * position.quantity
            else:
                gross_pnl = (position.entry_price - adjusted_exit_price) * position.quantity

            total_commission = position.commission_paid + exit_commission
            realized_pnl = gross_pnl - total_commission

            # Create completed trade record
            trade = Trade(
                symbol=position.symbol,
                position_type=position.position_type,
                entry_price=position.entry_price,
                exit_price=adjusted_exit_price,
                quantity=position.quantity,
                realized_pnl=realized_pnl,
                commission=total_commission,
                opened_at=position.opened_at,
                closed_at=datetime.now(timezone.utc),
                pattern_strength=position.pattern_strength,
                pattern_type=position.pattern_type,
                exit_reason=exit_reason
            )

            # Calculate hold time
            hold_time = trade.closed_at - trade.opened_at
            trade.hold_time_minutes = hold_time.total_seconds() / 60

            # Calculate risk-reward ratio
            risk = position.risk_amount
            if risk > 0:
                trade.risk_reward_ratio = abs(realized_pnl) / risk

            # Update balance (add back the exit trade value minus exit commission)
            trade_value = adjusted_exit_price * position.quantity
            self.current_balance += trade_value - exit_commission
            self.total_pnl += realized_pnl
            self.daily_pnl += realized_pnl

            # Update drawdown tracking
            if self.current_balance > self.peak_balance:
                self.peak_balance = self.current_balance

            current_drawdown = ((self.peak_balance - self.current_balance) / self.peak_balance) * 100
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown

            # Store completed trade and remove position
            self.completed_trades.append(trade)
            del self.positions[position_id]

            logger.info(f"Closed {position.position_type.value} position for {position.symbol}: "
                       f"P&L: ${realized_pnl:.2f}, Exit reason: {exit_reason}")

            return True, "Position closed successfully", trade

        except Exception as e:
            logger.error(f"Error closing position {position_id}: {e}")
            return False, str(e), None

    def _calculate_take_profit_levels(self, position: Position, pattern_data: Dict) -> List[Tuple[float, float]]:
        """Calculate take profit levels based on risk-reward ratios"""
        take_profit_levels = []

        risk_per_share = abs(position.entry_price - position.stop_loss)
        if risk_per_share <= 0:
            return take_profit_levels

        tp_percentages = RISK_CONFIG['take_profit_levels']
        tp_ratios = RISK_CONFIG['take_profit_ratios']

        # Adjust ratios based on pattern strength if available
        strength_multiplier = 1.0
        if pattern_data and 'pattern_strength' in pattern_data:
            strength = pattern_data['pattern_strength']
            if strength >= 3.0:
                strength_multiplier = 1.2  # Increase targets for strong patterns
            elif strength >= 2.5:
                strength_multiplier = 1.1

        for i, (percentage, ratio) in enumerate(zip(tp_percentages, tp_ratios)):
            adjusted_ratio = ratio * strength_multiplier

            if position.position_type == PositionType.LONG:
                tp_price = position.entry_price + (risk_per_share * adjusted_ratio)
            else:
                tp_price = position.entry_price - (risk_per_share * adjusted_ratio)

            tp_quantity = position.quantity * (percentage / 100)
            take_profit_levels.append((tp_price, tp_quantity))

        return take_profit_levels

    def update_positions(self, price_data: Dict[str, float]):
        """Update all positions with current prices"""
        for position in self.positions.values():
            if position.symbol in price_data:
                current_price = price_data[position.symbol]
                position.update_unrealized_pnl(current_price)

    def get_portfolio_summary(self) -> Dict:
        """Get portfolio summary statistics"""
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())

        # Calculate total market value of open positions
        total_position_value = sum(pos.current_price * pos.quantity for pos in self.positions.values())

        # Total portfolio value = cash + market value of positions
        total_value = self.current_balance + total_position_value

        # Calculate win rate
        winning_trades = [t for t in self.completed_trades if t.realized_pnl > 0]
        win_rate = (len(winning_trades) / len(self.completed_trades)) * 100 if self.completed_trades else 0

        # Calculate average trade metrics
        avg_win = sum(t.realized_pnl for t in winning_trades) / len(winning_trades) if winning_trades else 0
        losing_trades = [t for t in self.completed_trades if t.realized_pnl <= 0]
        avg_loss = sum(t.realized_pnl for t in losing_trades) / len(losing_trades) if losing_trades else 0

        return {
            'starting_balance': self.starting_balance,
            'current_balance': self.current_balance,
            'total_value': total_value,
            'total_pnl': self.total_pnl,
            'unrealized_pnl': total_unrealized_pnl,
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown,
            'open_positions': len(self.positions),
            'total_trades': len(self.completed_trades),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else 0,
            'allocation_percent': self.get_position_allocation_percent()
        }
