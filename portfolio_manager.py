"""
Portfolio Management System for Cryptocurrency Trading Bot
Coordinates signal generation, position management, and risk control
"""
from typing import Dict, List, Optional
from datetime import datetime, timezone
import logging
import threading

from paper_trader import PaperTradingEngine, PositionType
from trading_signals import SignalGenerator, TradingSignal
from risk_manager import RiskManager
from screener import CryptoScreener
from bybit_api import BybitAPI
from config import TRADING_CONFIG, SIGNAL_CONFIG

logger = logging.getLogger(__name__)

class PortfolioManager:
    """Main portfolio management system"""
    
    def __init__(self, timeframe_config: Dict = None):
        self.timeframe_config = timeframe_config or {
            'bybit_interval': '240',
            'interval_minutes': 240,
            'display_name': '4-Hour',
            'short_name': '4H'
        }
        
        # Initialize components
        self.trading_engine = PaperTradingEngine()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager(self.trading_engine)
        self.screener = CryptoScreener(timeframe_config=self.timeframe_config)
        self.api = BybitAPI(timeframe_config=self.timeframe_config)
        
        # State tracking
        self.last_signal_generation = None
        self.active_signals: List[TradingSignal] = []
        self.lock = threading.Lock()
        
        logger.info(f"Portfolio manager initialized for {self.timeframe_config['display_name']} timeframe")
    
    def update_portfolio(self) -> Dict:
        """Main portfolio update cycle"""
        try:
            with self.lock:
                # Check for new trading signals first
                new_signals = self._check_for_new_signals()

                # Get current prices for all positions and new signals
                additional_symbols = [signal.symbol for signal in new_signals] if new_signals else None
                current_prices = self._get_current_prices(additional_symbols=additional_symbols)

                # Update positions with current prices
                self.trading_engine.update_positions(current_prices)

                # Check risk management first
                risk_actions = self.risk_manager.check_all_positions(current_prices)
                if risk_actions:
                    risk_results = self.risk_manager.execute_risk_actions(risk_actions, current_prices)
                    logger.info(f"Executed {len(risk_results)} risk management actions")

                # Process new signals if any
                if new_signals:
                    self._process_new_signals(new_signals, current_prices)

                # Reset daily tracking if needed
                if self.risk_manager.should_reset_daily_tracking():
                    self.risk_manager.reset_daily_tracking()

                # Return portfolio status
                return self._get_portfolio_status()

        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
            return {'error': str(e)}
    
    def _get_current_prices(self, additional_symbols: List[str] = None) -> Dict[str, float]:
        """Get current prices for all symbols we're tracking"""
        prices = {}

        # Get prices for open positions
        for position in self.trading_engine.positions.values():
            try:
                price_data = self.api.get_current_price_and_volume(position.symbol)
                if price_data and 'price' in price_data:
                    prices[position.symbol] = price_data['price']
            except Exception as e:
                logger.error(f"Error getting price for {position.symbol}: {e}")

        # Get prices for active signals
        for signal in self.active_signals:
            if signal.symbol not in prices:
                try:
                    price_data = self.api.get_current_price_and_volume(signal.symbol)
                    if price_data and 'price' in price_data:
                        prices[signal.symbol] = price_data['price']
                        logger.debug(f"Got price for {signal.symbol}: ${price_data['price']}")
                    else:
                        logger.warning(f"No price data returned for {signal.symbol}")
                except Exception as e:
                    logger.error(f"Error getting price for {signal.symbol}: {e}")

        # Get prices for additional symbols (e.g., new signals)
        if additional_symbols:
            for symbol in additional_symbols:
                if symbol not in prices:
                    try:
                        price_data = self.api.get_current_price_and_volume(symbol)
                        if price_data and 'price' in price_data:
                            prices[symbol] = price_data['price']
                            logger.debug(f"Got price for additional symbol {symbol}: ${price_data['price']}")
                        else:
                            logger.warning(f"No price data returned for additional symbol {symbol}")
                    except Exception as e:
                        logger.error(f"Error getting price for additional symbol {symbol}: {e}")

        logger.debug(f"Retrieved prices for {len(prices)} symbols: {list(prices.keys())}")
        return prices
    
    def _check_for_new_signals(self) -> List[TradingSignal]:
        """Check for new trading signals from completed candles"""
        # Only generate signals when we have a new completed candle
        if not self.screener.check_candle_transition():
            return []
        
        logger.info("New candle detected - checking for trading signals")
        
        # Get patterns from previous completed candle
        self.screener.update_previous_patterns()
        patterns = self.screener.get_previous_patterns()
        
        if not patterns:
            logger.info("No patterns found in previous candle")
            return []
        
        # Generate signals
        signals = self.signal_generator.generate_signals_from_patterns(patterns)
        
        # Filter out signals for symbols we already have positions in
        filtered_signals = []
        existing_symbols = {pos.symbol for pos in self.trading_engine.positions.values()}
        
        for signal in signals:
            if signal.symbol not in existing_symbols:
                filtered_signals.append(signal)
            else:
                logger.info(f"Skipping signal for {signal.symbol} - already have position")
        
        self.last_signal_generation = datetime.now(timezone.utc)
        self.active_signals = filtered_signals
        
        logger.info(f"Generated {len(filtered_signals)} new trading signals")
        return filtered_signals
    
    def _process_new_signals(self, signals: List[TradingSignal], current_prices: Dict[str, float]):
        """Process new trading signals and open positions"""
        for signal in signals:
            try:
                # Check if we can still open positions
                if len(self.trading_engine.positions) >= TRADING_CONFIG['max_positions']:
                    logger.info("Maximum positions reached - skipping remaining signals")
                    break
                
                # Get current price for entry validation
                current_price = current_prices.get(signal.symbol)
                if not current_price:
                    logger.warning(f"No current price available for {signal.symbol}")
                    continue
                
                # Check if entry is still valid (price hasn't moved too far)
                if not self._is_entry_still_valid(signal, current_price):
                    logger.info(f"Entry no longer valid for {signal.symbol}")
                    continue
                
                # Open position
                success, message, position = self.trading_engine.open_position(
                    symbol=signal.symbol,
                    position_type=signal.signal_type,
                    entry_price=current_price,  # Use current price for entry
                    stop_loss=signal.stop_loss,
                    pattern_data=signal.pattern_data
                )
                
                if success:
                    logger.info(f"Opened position for {signal.symbol}: {message}")
                else:
                    logger.warning(f"Failed to open position for {signal.symbol}: {message}")
                    
            except Exception as e:
                logger.error(f"Error processing signal for {signal.symbol}: {e}")
    
    def _is_entry_still_valid(self, signal: TradingSignal, current_price: float) -> bool:
        """Check if entry is still valid based on current price"""
        # Allow some tolerance for price movement (crypto markets are volatile)
        tolerance_percent = 3.0  # 3% tolerance

        price_diff_percent = abs(current_price - signal.entry_price) / signal.entry_price * 100

        if price_diff_percent > tolerance_percent:
            logger.info(f"Entry invalid for {signal.symbol}: price moved {price_diff_percent:.2f}% (limit: {tolerance_percent}%)")
            return False
        
        # For longs, don't enter if price is above stop loss
        if signal.signal_type == PositionType.LONG and current_price <= signal.stop_loss:
            return False
        
        # For shorts, don't enter if price is below stop loss
        if signal.signal_type == PositionType.SHORT and current_price >= signal.stop_loss:
            return False
        
        return True
    
    def _get_portfolio_status(self) -> Dict:
        """Get comprehensive portfolio status"""
        portfolio_summary = self.trading_engine.get_portfolio_summary()

        # Debug logging
        logger.info(f"Getting portfolio status: {len(self.trading_engine.positions)} positions in trading engine")

        # Add position details
        positions = []
        for position in self.trading_engine.positions.values():
            positions.append({
                'id': position.id,
                'symbol': position.symbol,
                'type': position.position_type.value,
                'entry_price': position.entry_price,
                'current_price': position.current_price,
                'quantity': position.quantity,
                'unrealized_pnl': position.unrealized_pnl,
                'stop_loss': position.stop_loss,
                'trailing_stop': position.trailing_stop,
                'trailing_stop_activated': position.trailing_stop_activated,
                'pattern_type': position.pattern_type,
                'pattern_strength': position.pattern_strength,
                'opened_at': position.opened_at.isoformat(),
                'risk_amount': position.risk_amount
            })
        
        # Add recent trades
        recent_trades = []
        for trade in self.trading_engine.completed_trades[-10:]:  # Last 10 trades
            recent_trades.append({
                'id': trade.id,
                'symbol': trade.symbol,
                'type': trade.position_type.value,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'quantity': trade.quantity,
                'realized_pnl': trade.realized_pnl,
                'pattern_type': trade.pattern_type,
                'pattern_strength': trade.pattern_strength,
                'exit_reason': trade.exit_reason,
                'hold_time_minutes': trade.hold_time_minutes,
                'risk_reward_ratio': trade.risk_reward_ratio,
                'opened_at': trade.opened_at.isoformat(),
                'closed_at': trade.closed_at.isoformat()
            })
        
        # Add active signals
        active_signals = []
        for signal in self.active_signals:
            active_signals.append({
                'symbol': signal.symbol,
                'type': signal.signal_type.value,
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'confidence': signal.confidence,
                'pattern_strength': signal.pattern_data.get('pattern_strength', 0),
                'pattern_type': signal.pattern_data.get('pattern_type', ''),
                'generated_at': signal.generated_at.isoformat()
            })
        
        return {
            'portfolio_summary': portfolio_summary,
            'positions': positions,
            'recent_trades': recent_trades,
            'active_signals': active_signals,
            'last_update': datetime.now(timezone.utc).isoformat(),
            'timeframe': self.timeframe_config['display_name']
        }
    
    def get_performance_metrics(self) -> Dict:
        """Get detailed performance metrics"""
        trades = self.trading_engine.completed_trades
        if not trades:
            return {'message': 'No completed trades yet'}
        
        # Calculate metrics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t.realized_pnl > 0]
        losing_trades = [t for t in trades if t.realized_pnl <= 0]
        
        win_rate = len(winning_trades) / total_trades * 100
        avg_win = sum(t.realized_pnl for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t.realized_pnl for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # Risk-reward metrics
        avg_rr_ratio = sum(t.risk_reward_ratio for t in trades) / total_trades
        avg_hold_time = sum(t.hold_time_minutes for t in trades) / total_trades
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_risk_reward_ratio': avg_rr_ratio,
            'avg_hold_time_minutes': avg_hold_time,
            'total_pnl': self.trading_engine.total_pnl,
            'max_drawdown': self.trading_engine.max_drawdown
        }
