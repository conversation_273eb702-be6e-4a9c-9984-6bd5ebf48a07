"""
Main screening engine for cryptocurrency trading opportunities
"""
from datetime import datetime, timezone
from typing import List, Dict, Optional
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

from bybit_api import BybitAPI
from technical_analysis import TechnicalAnalysis

logger = logging.getLogger(__name__)

class CryptoScreener:
    def __init__(self, timeframe_config=None, max_ema_distance: float = 5.0, min_pattern_strength: float = 1.5):
        self.timeframe_config = timeframe_config or {
            'bybit_interval': '240',
            'interval_minutes': 240,
            'display_name': '4-Hour',
            'short_name': '4H'
        }
        self.api = BybitAPI(timeframe_config=self.timeframe_config)
        self.ta = TechnicalAnalysis()
        self.max_ema_distance = max_ema_distance  # Maximum % distance from EMA to consider
        self.min_pattern_strength = min_pattern_strength  # Minimum pattern strength

        # Data storage
        self.current_patterns = []  # Developing patterns in current candle
        self.previous_patterns = []  # Confirmed patterns from previous candle
        self.pairs_cache = []
        self.last_update = None
        self.lock = threading.Lock()

    def get_trading_pairs(self) -> List[str]:
        """Get all trading pairs, with caching"""
        if not self.pairs_cache:
            self.pairs_cache = self.api.get_perpetual_pairs()
            logger.info(f"Loaded {len(self.pairs_cache)} trading pairs")
        return self.pairs_cache

    def analyze_single_pair(self, symbol: str, analyze_current: bool = True) -> Optional[Dict]:
        """Analyze a single trading pair for patterns"""
        try:
            # Get historical data for EMA calculation
            df = self.api.get_kline_data(symbol, limit=100)
            if df.empty or len(df) < 55:
                return None

            # Calculate 55 EMA
            df['ema_55'] = self.ta.calculate_ema(df['close'], 55)
            current_ema = df['ema_55'].iloc[-1]

            if analyze_current:
                # Analyze current developing candle
                current_candle = self.api.get_current_candle_info(symbol)
                if not current_candle:
                    return None

                analysis = self.ta.analyze_candle_pattern(current_candle, current_ema)
            else:
                # Analyze previous completed candle
                if len(df) < 2:
                    return None

                # Get volume data for previous candle (use current ticker as approximation)
                ticker_data = self.api.get_current_price_and_volume(symbol)

                prev_candle = {
                    'symbol': symbol,
                    'timestamp': df['timestamp'].iloc[-2],
                    'open': df['open'].iloc[-2],
                    'high': df['high'].iloc[-2],
                    'low': df['low'].iloc[-2],
                    'close': df['close'].iloc[-2],
                    'turnover_24h': ticker_data.get('turnover_24h', 0),
                    'is_developing': False
                }

                # Use EMA from previous candle
                prev_ema = df['ema_55'].iloc[-2]
                analysis = self.ta.analyze_candle_pattern(prev_candle, prev_ema)

            # Filter based on criteria
            if (analysis['pattern_type'] and
                analysis['pattern_strength'] >= self.min_pattern_strength and
                abs(analysis['ema_distance']) <= self.max_ema_distance):

                return analysis

            return None

        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return None

    def scan_all_pairs(self, analyze_current: bool = True, max_workers: int = 10) -> List[Dict]:
        """Scan all trading pairs for patterns using multithreading"""
        pairs = self.get_trading_pairs()
        results = []

        logger.info(f"Scanning {len(pairs)} pairs for {'current' if analyze_current else 'previous'} patterns...")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_symbol = {
                executor.submit(self.analyze_single_pair, symbol, analyze_current): symbol
                for symbol in pairs
            }

            # Collect results
            completed = 0
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result(timeout=30)  # 30 second timeout per pair
                    if result:
                        results.append(result)
                    completed += 1

                    if completed % 50 == 0:
                        logger.info(f"Completed {completed}/{len(pairs)} pairs")

                except Exception as e:
                    logger.error(f"Error processing {symbol}: {e}")

        # Sort by pattern strength (strongest first)
        results.sort(key=lambda x: x['pattern_strength'], reverse=True)

        logger.info(f"Found {len(results)} patterns")
        return results

    def update_current_patterns(self):
        """Update patterns for current developing candle"""
        with self.lock:
            self.current_patterns = self.scan_all_pairs(analyze_current=True)
            self.last_update = datetime.now(timezone.utc)

    def update_previous_patterns(self):
        """Update patterns for previous completed candle"""
        with self.lock:
            self.previous_patterns = self.scan_all_pairs(analyze_current=False)

    def get_current_patterns(self) -> List[Dict]:
        """Get current developing patterns (thread-safe)"""
        with self.lock:
            return self.current_patterns.copy()

    def get_previous_patterns(self) -> List[Dict]:
        """Get previous completed patterns (thread-safe)"""
        with self.lock:
            return self.previous_patterns.copy()

    def check_candle_transition(self) -> bool:
        """Check if we've transitioned to a new candle"""
        return self.api.is_new_candle()

    def format_pattern_for_display(self, pattern: Dict) -> Dict:
        """Format pattern data for dashboard display"""
        return {
            'symbol': pattern['symbol'],
            'pattern_type': pattern['pattern_type'].replace('_', ' ').title(),
            'strength': f"{pattern['pattern_strength']:.1f}x",
            'base_strength': f"{pattern.get('base_strength', 0):.1f}x",
            'volume_multiplier': f"{pattern.get('volume_multiplier', 1.0):.1f}x",
            'ema_distance': pattern['ema_distance'],
            'ema_distance_abs': abs(pattern['ema_distance']),
            'price': f"${pattern['close_price']:.4f}",
            'turnover_24h': pattern.get('turnover_24h', 0),
            'turnover_24h_formatted': pattern.get('turnover_24h_formatted', '$0'),
            'bybit_url': f"https://www.bybit.com/trade/usdt/{pattern['symbol']}",
            'timestamp': pattern['timestamp'].strftime('%H:%M UTC') if hasattr(pattern['timestamp'], 'strftime') else str(pattern['timestamp'])
        }

    def get_dashboard_data(self) -> Dict:
        """Get formatted data for dashboard display"""
        current = [self.format_pattern_for_display(p) for p in self.get_current_patterns()]
        previous = [self.format_pattern_for_display(p) for p in self.get_previous_patterns()]

        return {
            'current_patterns': current,
            'previous_patterns': previous,
            'last_update': self.last_update.strftime('%Y-%m-%d %H:%M:%S UTC') if self.last_update else 'Never',
            'total_pairs': len(self.get_trading_pairs())
        }
