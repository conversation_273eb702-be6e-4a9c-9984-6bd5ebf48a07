"""
Technical analysis functions for cryptocurrency trading
"""
import pandas as pd
from typing import Dict
import logging

logger = logging.getLogger(__name__)

class TechnicalAnalysis:

    @staticmethod
    def calculate_ema(data: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return data.ewm(span=period, adjust=False).mean()

    @staticmethod
    def is_pinbar(open_price: float, high: float, low: float, close: float,
                  min_wick_ratio: float = 2.0, body_position_threshold: float = 0.33) -> Dict:
        """
        Detect pinbar candlestick pattern

        Args:
            open_price, high, low, close: OHLC values
            min_wick_ratio: Minimum ratio of wick to body size
            body_position_threshold: Body must be in upper/lower 1/3 of range

        Returns:
            Dict with pinbar detection results
        """
        body_size = abs(close - open_price)
        total_range = high - low
        upper_wick = high - max(open_price, close)
        lower_wick = min(open_price, close) - low

        if total_range == 0 or body_size == 0:
            return {'is_pinbar': False, 'type': None, 'strength': 0}

        # Calculate body position in the total range
        body_top = max(open_price, close)
        body_bottom = min(open_price, close)
        body_position_from_top = (high - body_top) / total_range
        body_position_from_bottom = (body_bottom - low) / total_range

        # Check for bullish pinbar (long lower wick, body in upper part)
        if (lower_wick >= min_wick_ratio * body_size and
            body_position_from_top <= body_position_threshold):
            strength = lower_wick / body_size if body_size > 0 else 0
            return {
                'is_pinbar': True,
                'type': 'bullish_pinbar',
                'strength': round(strength, 2),
                'wick_size': lower_wick,
                'body_size': body_size
            }

        # Check for bearish pinbar (long upper wick, body in lower part)
        if (upper_wick >= min_wick_ratio * body_size and
            body_position_from_bottom <= body_position_threshold):
            strength = upper_wick / body_size if body_size > 0 else 0
            return {
                'is_pinbar': True,
                'type': 'bearish_pinbar',
                'strength': round(strength, 2),
                'wick_size': upper_wick,
                'body_size': body_size
            }

        return {'is_pinbar': False, 'type': None, 'strength': 0}

    @staticmethod
    def is_long_wick(open_price: float, high: float, low: float, close: float,
                     min_wick_ratio: float = 1.5) -> Dict:
        """
        Detect long wick patterns (not necessarily pinbars)

        Args:
            open_price, high, low, close: OHLC values
            min_wick_ratio: Minimum ratio of wick to body size

        Returns:
            Dict with long wick detection results
        """
        body_size = abs(close - open_price)
        upper_wick = high - max(open_price, close)
        lower_wick = min(open_price, close) - low

        if body_size == 0:
            body_size = 0.001  # Avoid division by zero for doji candles

        results = []

        # Check for long upper wick
        if upper_wick >= min_wick_ratio * body_size:
            strength = upper_wick / body_size
            results.append({
                'type': 'long_upper_wick',
                'strength': round(strength, 2),
                'wick_size': upper_wick
            })

        # Check for long lower wick
        if lower_wick >= min_wick_ratio * body_size:
            strength = lower_wick / body_size
            results.append({
                'type': 'long_lower_wick',
                'strength': round(strength, 2),
                'wick_size': lower_wick
            })

        if results:
            return {
                'has_long_wick': True,
                'patterns': results,
                'body_size': body_size
            }

        return {'has_long_wick': False, 'patterns': []}

    @staticmethod
    def calculate_ema_distance(price: float, ema_value: float) -> float:
        """Calculate percentage distance from EMA"""
        if ema_value == 0:
            return 0
        return round(((price - ema_value) / ema_value) * 100, 2)

    @staticmethod
    def calculate_volume_multiplier(turnover_24h: float) -> float:
        """
        Calculate volume multiplier based on 24h turnover
        Higher volume = higher multiplier (up to 2x)
        """
        if turnover_24h <= 0:
            return 1.0

        # Volume tiers (in USDT)
        if turnover_24h >= 100_000_000:    # 100M+ USDT
            return 2.0
        elif turnover_24h >= 50_000_000:   # 50M+ USDT
            return 1.8
        elif turnover_24h >= 20_000_000:   # 20M+ USDT
            return 1.6
        elif turnover_24h >= 10_000_000:   # 10M+ USDT
            return 1.4
        elif turnover_24h >= 5_000_000:    # 5M+ USDT
            return 1.2
        elif turnover_24h >= 1_000_000:    # 1M+ USDT
            return 1.1
        else:
            return 1.0

    @staticmethod
    def format_volume(turnover_24h: float) -> str:
        """Format volume for display"""
        if turnover_24h >= 1_000_000_000:
            return f"${turnover_24h/1_000_000_000:.1f}B"
        elif turnover_24h >= 1_000_000:
            return f"${turnover_24h/1_000_000:.1f}M"
        elif turnover_24h >= 1_000:
            return f"${turnover_24h/1_000:.1f}K"
        else:
            return f"${turnover_24h:.0f}"

    @staticmethod
    def analyze_candle_pattern(candle_data: Dict, ema_55: float) -> Dict:
        """
        Comprehensive analysis of a single candle for patterns

        Args:
            candle_data: Dict with OHLC data and volume
            ema_55: 55-period EMA value

        Returns:
            Dict with complete pattern analysis including volume-weighted strength
        """
        open_price = candle_data['open']
        high = candle_data['high']
        low = candle_data['low']
        close = candle_data['close']
        turnover_24h = candle_data.get('turnover_24h', 0)

        # Analyze pinbar pattern
        pinbar_result = TechnicalAnalysis.is_pinbar(open_price, high, low, close)

        # Analyze long wick patterns
        long_wick_result = TechnicalAnalysis.is_long_wick(open_price, high, low, close)

        # Calculate distance from EMA
        ema_distance = TechnicalAnalysis.calculate_ema_distance(close, ema_55)

        # Determine primary pattern type and base strength
        pattern_type = None
        base_strength = 0

        if pinbar_result['is_pinbar']:
            pattern_type = pinbar_result['type']
            base_strength = pinbar_result['strength']
        elif long_wick_result['has_long_wick']:
            # Use the strongest long wick pattern
            strongest = max(long_wick_result['patterns'], key=lambda x: x['strength'])
            pattern_type = strongest['type']
            base_strength = strongest['strength']

        # Calculate volume multiplier and final strength
        volume_multiplier = TechnicalAnalysis.calculate_volume_multiplier(turnover_24h)
        final_strength = base_strength * volume_multiplier

        return {
            'symbol': candle_data.get('symbol', ''),
            'timestamp': candle_data.get('timestamp', ''),
            'pattern_type': pattern_type,
            'pattern_strength': final_strength,
            'base_strength': base_strength,
            'volume_multiplier': volume_multiplier,
            'turnover_24h': turnover_24h,
            'turnover_24h_formatted': TechnicalAnalysis.format_volume(turnover_24h),
            'ema_distance': ema_distance,
            'ema_55': ema_55,
            'close_price': close,
            'pinbar_analysis': pinbar_result,
            'long_wick_analysis': long_wick_result,
            'is_developing': candle_data.get('is_developing', False)
        }
