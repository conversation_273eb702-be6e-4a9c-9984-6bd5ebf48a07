<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto {{ timeframe_config.short_name }} Screener</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .pattern-strength { font-weight: bold; }
        .bullish { color: #28a745; }
        .bearish { color: #dc3545; }
        .neutral { color: #6c757d; }
        .table-container { max-height: 400px; overflow-y: auto; }
        .status-bar { background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .sortable { cursor: pointer; user-select: none; }
        .sortable:hover { background-color: #495057; }
        .sort-arrow { margin-left: 5px; opacity: 0.5; }
        .sort-asc::after { content: ' ↑'; }
        .sort-desc::after { content: ' ↓'; }
        .volume-high { background-color: rgba(40, 167, 69, 0.1); }
        .volume-medium { background-color: rgba(255, 193, 7, 0.1); }
        .volume-low { background-color: rgba(108, 117, 125, 0.1); }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <h1 class="text-center mb-4">🚀 Crypto {{ timeframe_config.short_name }} Pattern Screener</h1>
        <p class="text-center text-muted mb-4">{{ timeframe_config.display_name }} Timeframe Analysis</p>

        <div class="status-bar">
            <div class="row">
                <div class="col-md-4">
                    <strong>Last Update:</strong> <span id="lastUpdate">Loading...</span>
                </div>
                <div class="col-md-4">
                    <strong>Total Pairs:</strong> <span id="totalPairs">-</span>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary btn-sm" onclick="refreshData()">🔄 Refresh</button>
                    <span class="badge bg-success ms-2" id="autoRefresh">Auto-refresh: ON</span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">📈 Current Candle Patterns (<span id="currentCount">0</span>)</h5>
                        <small>Developing patterns in the current {{ timeframe_config.display_name.lower() }} candle</small>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-container">
                            <table class="table table-striped table-hover mb-0" id="currentTable">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th class="sortable" data-sort="symbol">Pair</th>
                                        <th class="sortable" data-sort="pattern_type">Pattern</th>
                                        <th class="sortable" data-sort="strength" data-type="number">Strength</th>
                                        <th class="sortable" data-sort="turnover_24h" data-type="number">24h Volume</th>
                                        <th class="sortable" data-sort="ema_distance_abs" data-type="number">EMA Distance</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="currentPatterns">
                                    <tr><td colspan="6" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">✅ Previous Candle Patterns (<span id="previousCount">0</span>)</h5>
                        <small>Confirmed patterns from the last completed {{ timeframe_config.display_name.lower() }} candle</small>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-container">
                            <table class="table table-striped table-hover mb-0" id="previousTable">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th class="sortable" data-sort="symbol">Pair</th>
                                        <th class="sortable" data-sort="pattern_type">Pattern</th>
                                        <th class="sortable" data-sort="strength" data-type="number">Strength</th>
                                        <th class="sortable" data-sort="turnover_24h" data-type="number">24h Volume</th>
                                        <th class="sortable" data-sort="ema_distance_abs" data-type="number">EMA Distance</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="previousPatterns">
                                    <tr><td colspan="6" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📊 Pattern Detection Criteria</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Pinbars:</strong> Wick ≥ 2x body size, body in upper/lower 1/3
                            </div>
                            <div class="col-md-3">
                                <strong>Long Wicks:</strong> Single wick ≥ 1.5x body size
                            </div>
                            <div class="col-md-3">
                                <strong>EMA Filter:</strong> Within ±5% of 55 EMA
                            </div>
                            <div class="col-md-3">
                                <strong>Volume Boost:</strong> Higher 24h volume = higher strength (up to 2x)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval;
        let currentSortColumn = 'strength';
        let currentSortDirection = 'desc';
        let previousSortColumn = 'strength';
        let previousSortDirection = 'desc';

        function getPatternClass(patternType) {
            if (patternType.includes('Bullish') || patternType.includes('Lower')) return 'bullish';
            if (patternType.includes('Bearish') || patternType.includes('Upper')) return 'bearish';
            return 'neutral';
        }

        function getVolumeClass(turnover) {
            if (turnover >= 50000000) return 'volume-high';
            if (turnover >= 10000000) return 'volume-medium';
            return 'volume-low';
        }

        function sortPatterns(patterns, column, direction) {
            return patterns.sort((a, b) => {
                let aVal = a[column];
                let bVal = b[column];

                // Handle numeric values
                if (column === 'strength' || column === 'turnover_24h' || column === 'ema_distance_abs') {
                    aVal = parseFloat(aVal.toString().replace(/[^0-9.-]/g, ''));
                    bVal = parseFloat(bVal.toString().replace(/[^0-9.-]/g, ''));
                }

                if (direction === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });
        }

        function updateSortHeaders(tableId, column, direction) {
            const table = document.getElementById(tableId);
            const headers = table.querySelectorAll('th.sortable');

            headers.forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            const activeHeader = table.querySelector(`th[data-sort="${column}"]`);
            if (activeHeader) {
                activeHeader.classList.add(direction === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        function renderPatterns(patterns, tableId) {
            const tbody = document.getElementById(tableId);

            if (patterns.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No patterns detected</td></tr>';
                return;
            }

            // Sort patterns
            let sortColumn, sortDirection;
            if (tableId === 'currentPatterns') {
                sortColumn = currentSortColumn;
                sortDirection = currentSortDirection;
                updateSortHeaders('currentTable', sortColumn, sortDirection);
            } else {
                sortColumn = previousSortColumn;
                sortDirection = previousSortDirection;
                updateSortHeaders('previousTable', sortColumn, sortDirection);
            }

            const sortedPatterns = sortPatterns([...patterns], sortColumn, sortDirection);

            tbody.innerHTML = sortedPatterns.map(pattern => `
                <tr class="${getVolumeClass(pattern.turnover_24h)}">
                    <td><strong>${pattern.symbol}</strong></td>
                    <td><span class="pattern-strength ${getPatternClass(pattern.pattern_type)}">${pattern.pattern_type}</span></td>
                    <td>
                        <strong>${pattern.strength}</strong>
                        <small class="text-muted d-block">${pattern.base_strength} × ${pattern.volume_multiplier}</small>
                    </td>
                    <td>
                        <strong>${pattern.turnover_24h_formatted}</strong>
                    </td>
                    <td><span class="${pattern.ema_distance >= 0 ? 'text-success' : 'text-danger'}">${pattern.ema_distance > 0 ? '+' : ''}${pattern.ema_distance.toFixed(1)}%</span></td>
                    <td><a href="${pattern.bybit_url}" target="_blank" class="btn btn-sm btn-outline-primary">Trade</a></td>
                </tr>
            `).join('');
        }

        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }

                    renderPatterns(data.current_patterns, 'currentPatterns');
                    renderPatterns(data.previous_patterns, 'previousPatterns');

                    document.getElementById('currentCount').textContent = data.current_patterns.length;
                    document.getElementById('previousCount').textContent = data.previous_patterns.length;
                    document.getElementById('lastUpdate').textContent = data.last_update;
                    document.getElementById('totalPairs').textContent = data.total_pairs;
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                });
        }

        function refreshData() {
            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.error);
                    } else {
                        setTimeout(updateData, 2000); // Update display after 2 seconds
                    }
                });
        }

        function startAutoRefresh() {
            // Get update interval from server data
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    const updateInterval = (data.timeframe_config?.update_interval || 30) * 1000;
                    autoRefreshInterval = setInterval(updateData, updateInterval);
                    console.log(`Auto-refresh set to ${updateInterval/1000} seconds for ${data.timeframe_config?.display_name || '4-Hour'} timeframe`);
                })
                .catch(() => {
                    // Fallback to 30 seconds if can't get config
                    autoRefreshInterval = setInterval(updateData, 30000);
                });
        }

        function setupSortHandlers() {
            // Current table sort handlers
            document.querySelectorAll('#currentTable th.sortable').forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.sort;
                    if (currentSortColumn === column) {
                        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSortColumn = column;
                        currentSortDirection = 'desc';
                    }
                    updateData(); // Re-render with new sort
                });
            });

            // Previous table sort handlers
            document.querySelectorAll('#previousTable th.sortable').forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.sort;
                    if (previousSortColumn === column) {
                        previousSortDirection = previousSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        previousSortColumn = column;
                        previousSortDirection = 'desc';
                    }
                    updateData(); // Re-render with new sort
                });
            });
        }

        // Initialize
        updateData();
        startAutoRefresh();
        setupSortHandlers();
    </script>
</body>
</html>
