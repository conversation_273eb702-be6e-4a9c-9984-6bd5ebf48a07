"""
Test the dashboard with the portfolio accounting fix
Creates mock positions to verify dashboard display
"""
import sys
import logging
from datetime import datetime, timezone

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dashboard_with_accounting_fix():
    """Test dashboard display with corrected portfolio accounting"""
    print("Testing Dashboard with Portfolio Accounting Fix...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType, Position
        from trading_dashboard import TradingDashboard
        from config import TIMEFRAME_CONFIG
        
        # Create trading engine
        engine = PaperTradingEngine(starting_balance=1000.0)
        
        # Open some positions using the corrected accounting
        pattern_data = {'pattern_strength': 2.5, 'pattern_type': 'bullish_pinbar'}
        
        # Position 1: BTCUSDT
        success1, msg1, pos1 = engine.open_position(
            symbol='BTCUSDT',
            position_type=PositionType.LONG,
            entry_price=50000.0,
            stop_loss=48500.0,
            pattern_data=pattern_data
        )
        
        # Position 2: ETHUSDT  
        success2, msg2, pos2 = engine.open_position(
            symbol='ETHUSDT',
            position_type=PositionType.LONG,
            entry_price=3000.0,
            stop_loss=2850.0,
            pattern_data=pattern_data
        )
        
        if not (success1 and success2):
            print(f"✗ Failed to open positions: {msg1}, {msg2}")
            return False
        
        # Update prices to create some P&L
        price_updates = {
            'BTCUSDT': 51500.0,  # +$1500 profit
            'ETHUSDT': 2950.0    # -$50 loss
        }
        engine.update_positions(price_updates)
        
        # Create dashboard and override its trading engine
        timeframe_config = TIMEFRAME_CONFIG['4h']
        dashboard = TradingDashboard(timeframe='4h', timeframe_config=timeframe_config)
        dashboard.portfolio_manager.trading_engine = engine
        
        # Get portfolio data
        portfolio_data = dashboard.portfolio_manager._get_portfolio_status()
        
        # Print detailed breakdown
        print("\n" + "="*80)
        print("PORTFOLIO ACCOUNTING BREAKDOWN")
        print("="*80)
        
        summary = portfolio_data['portfolio_summary']
        positions = portfolio_data['positions']
        
        print(f"Starting Balance: $1,000.00")
        print(f"Current Balance (Cash): ${summary['current_balance']:,.2f}")
        print(f"Total Portfolio Value: ${summary['total_value']:,.2f}")
        print(f"Total P&L (Realized): ${summary['total_pnl']:+,.2f}")
        print(f"Unrealized P&L: ${summary['unrealized_pnl']:+,.2f}")
        
        print(f"\nOpen Positions: {len(positions)}")
        
        total_invested = 0
        total_market_value = 0
        
        for i, pos in enumerate(positions, 1):
            entry_value = pos['entry_price'] * pos['quantity']
            market_value = pos['current_price'] * pos['quantity']
            total_invested += entry_value
            total_market_value += market_value
            
            print(f"\nPosition {i}: {pos['symbol']}")
            print(f"  Entry: ${pos['entry_price']:,.2f} x {pos['quantity']:.6f} = ${entry_value:,.2f}")
            print(f"  Current: ${pos['current_price']:,.2f} x {pos['quantity']:.6f} = ${market_value:,.2f}")
            print(f"  Stop Loss: ${pos['stop_loss']:,.2f}")
            print(f"  Unrealized P&L: ${pos['unrealized_pnl']:+,.2f}")
        
        print(f"\nTotals:")
        print(f"  Total Invested: ${total_invested:,.2f}")
        print(f"  Total Market Value: ${total_market_value:,.2f}")
        print(f"  Cash Remaining: ${summary['current_balance']:,.2f}")
        print(f"  Portfolio Value: ${summary['current_balance'] + total_market_value:,.2f}")
        
        # Verify calculations
        total_commissions = sum(pos.commission_paid for pos in engine.positions.values())
        expected_cash = 1000.0 - total_invested - total_commissions
        expected_total_value = expected_cash + total_market_value
        
        print(f"\nVerification:")
        print(f"  Expected Cash: ${expected_cash:,.2f}")
        print(f"  Actual Cash: ${summary['current_balance']:,.2f}")
        print(f"  Expected Total Value: ${expected_total_value:,.2f}")
        print(f"  Actual Total Value: ${summary['total_value']:,.2f}")
        
        # Test console formatting
        console_data = dashboard._format_for_console(portfolio_data)
        
        print("\n" + "="*80)
        print("CONSOLE DASHBOARD OUTPUT")
        print("="*80)
        
        console_summary = console_data['summary']
        console_positions = console_data['positions']
        
        print(f"PORTFOLIO SUMMARY:")
        print(f"  Balance: {console_summary['balance']} | Total Value: {console_summary['total_value']}")
        print(f"  Total P&L: {console_summary['total_pnl']} | Daily P&L: {console_summary['daily_pnl']}")
        print(f"  Unrealized P&L: {console_summary['unrealized_pnl']} | Allocation: {console_summary['allocation']}")
        
        if console_positions:
            print(f"\nOPEN POSITIONS ({len(console_positions)}):")
            print(f"{'Symbol':<12} {'Type':<6} {'Entry':<12} {'Current':<12} {'Stop Loss':<12} {'P&L':<12} {'Pattern':<20}")
            print("-" * 100)
            for pos in console_positions:
                print(f"{pos['symbol']:<12} {pos['type']:<6} {pos['entry']:<12} {pos['current']:<12} "
                      f"{pos['stop_loss']:<12} {pos['pnl']:<12} {pos['pattern']:<20}")
        
        print("="*80)
        
        # Verify the fix worked
        cash_check = abs(summary['current_balance'] - expected_cash) < 0.01
        total_check = abs(summary['total_value'] - expected_total_value) < 0.01
        
        if cash_check and total_check:
            print("🎉 Portfolio accounting fix verified successfully!")
            print("✓ Current balance correctly represents available cash")
            print("✓ Total value correctly represents cash + market value")
            print("✓ Dashboard displays accurate portfolio information")
            return True
        else:
            print("✗ Portfolio accounting verification failed!")
            return False
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run dashboard test with accounting fix"""
    print("="*80)
    print("DASHBOARD TEST WITH PORTFOLIO ACCOUNTING FIX")
    print("="*80)
    
    success = test_dashboard_with_accounting_fix()
    
    if success:
        print("\n✅ All tests passed! The portfolio accounting fix is working correctly.")
        print("\nKey improvements:")
        print("• Current Balance now represents actual available cash")
        print("• Total Value = Cash + Market Value of Positions")
        print("• Portfolio values are now accurate and meaningful")
        print("• Dashboard displays correct financial information")
    else:
        print("\n❌ Tests failed. Please check the implementation.")
    
    print("="*80)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
