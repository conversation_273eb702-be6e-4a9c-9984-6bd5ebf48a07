"""
Comprehensive test for portfolio accounting fix
Tests position opening, price updates, and position closing
"""
import sys
import logging
from datetime import datetime, timezone

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_portfolio_accounting():
    """Test complete portfolio accounting flow"""
    print("Testing Portfolio Accounting Fix...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType
        
        # Create engine with $1000 starting balance
        engine = PaperTradingEngine(starting_balance=1000.0)
        
        print(f"✓ Starting Balance: ${engine.current_balance:.2f}")
        print(f"✓ Starting Total P&L: ${engine.total_pnl:.2f}")
        
        # Test 1: Open a position
        print("\n--- TEST 1: Opening Position ---")
        
        # Mock pattern data
        pattern_data = {
            'pattern_strength': 2.5,
            'pattern_type': 'bullish_pinbar'
        }
        
        # Open a long position: Entry $50000, Stop $48500, Risk = $1500 per unit
        success, message, position = engine.open_position(
            symbol='BTCUSDT',
            position_type=PositionType.LONG,
            entry_price=50000.0,
            stop_loss=48500.0,
            pattern_data=pattern_data
        )
        
        if not success:
            print(f"✗ Failed to open position: {message}")
            return False
        
        print(f"✓ Position opened: {message}")
        print(f"✓ Position quantity: {position.quantity:.6f}")
        print(f"✓ Position entry price: ${position.entry_price:.2f}")
        print(f"✓ Trade amount: ${position.entry_price * position.quantity:.2f}")
        print(f"✓ Commission paid: ${position.commission_paid:.2f}")
        
        # Check balance after opening
        trade_amount = position.entry_price * position.quantity
        expected_balance = 1000.0 - trade_amount - position.commission_paid
        
        print(f"✓ Current balance after opening: ${engine.current_balance:.2f}")
        print(f"✓ Expected balance: ${expected_balance:.2f}")
        
        if abs(engine.current_balance - expected_balance) > 0.01:
            print(f"✗ Balance mismatch! Expected: ${expected_balance:.2f}, Got: ${engine.current_balance:.2f}")
            return False
        
        # Get portfolio summary after opening
        summary = engine.get_portfolio_summary()
        position_value = position.current_price * position.quantity
        expected_total_value = engine.current_balance + position_value
        
        print(f"✓ Position market value: ${position_value:.2f}")
        print(f"✓ Total portfolio value: ${summary['total_value']:.2f}")
        print(f"✓ Expected total value: ${expected_total_value:.2f}")
        print(f"✓ Unrealized P&L: ${summary['unrealized_pnl']:.2f}")
        print(f"✓ Total P&L (realized): ${summary['total_pnl']:.2f}")
        
        if abs(summary['total_value'] - expected_total_value) > 0.01:
            print(f"✗ Total value mismatch!")
            return False
        
        # Test 2: Price movement (profit scenario)
        print("\n--- TEST 2: Price Movement (Profit) ---")
        
        # Update position with higher price
        new_price = 52000.0  # $2000 profit per unit
        engine.update_positions({'BTCUSDT': new_price})
        
        updated_position = list(engine.positions.values())[0]
        expected_unrealized_pnl = (new_price - position.entry_price) * position.quantity - position.commission_paid
        
        print(f"✓ New price: ${new_price:.2f}")
        print(f"✓ Updated unrealized P&L: ${updated_position.unrealized_pnl:.2f}")
        print(f"✓ Expected unrealized P&L: ${expected_unrealized_pnl:.2f}")
        
        if abs(updated_position.unrealized_pnl - expected_unrealized_pnl) > 0.01:
            print(f"✗ Unrealized P&L calculation error!")
            return False
        
        # Check portfolio summary with profit
        summary_profit = engine.get_portfolio_summary()
        new_position_value = new_price * position.quantity
        expected_total_value_profit = engine.current_balance + new_position_value
        
        print(f"✓ New position market value: ${new_position_value:.2f}")
        print(f"✓ Total portfolio value with profit: ${summary_profit['total_value']:.2f}")
        print(f"✓ Expected total value: ${expected_total_value_profit:.2f}")
        
        if abs(summary_profit['total_value'] - expected_total_value_profit) > 0.01:
            print(f"✗ Total value with profit mismatch!")
            return False
        
        # Test 3: Price movement (loss scenario)
        print("\n--- TEST 3: Price Movement (Loss) ---")
        
        # Update position with lower price
        loss_price = 49000.0  # $1000 loss per unit
        engine.update_positions({'BTCUSDT': loss_price})
        
        updated_position = list(engine.positions.values())[0]
        expected_unrealized_pnl_loss = (loss_price - position.entry_price) * position.quantity - position.commission_paid
        
        print(f"✓ Loss price: ${loss_price:.2f}")
        print(f"✓ Updated unrealized P&L: ${updated_position.unrealized_pnl:.2f}")
        print(f"✓ Expected unrealized P&L: ${expected_unrealized_pnl_loss:.2f}")
        
        if abs(updated_position.unrealized_pnl - expected_unrealized_pnl_loss) > 0.01:
            print(f"✗ Unrealized P&L loss calculation error!")
            return False
        
        # Test 4: Close position
        print("\n--- TEST 4: Closing Position ---")
        
        # Close at current price (loss scenario)
        exit_price = 49000.0
        position_id = list(engine.positions.keys())[0]
        
        # Record values before closing
        balance_before_close = engine.current_balance
        total_pnl_before = engine.total_pnl
        
        success, message, trade = engine.close_position(position_id, exit_price, "Test close")
        
        if not success:
            print(f"✗ Failed to close position: {message}")
            return False
        
        print(f"✓ Position closed: {message}")
        print(f"✓ Realized P&L: ${trade.realized_pnl:.2f}")
        print(f"✓ Exit price: ${trade.exit_price:.2f}")
        print(f"✓ Total commission: ${trade.commission:.2f}")
        
        # Check balance after closing
        exit_trade_value = trade.exit_price * trade.quantity
        expected_balance_after_close = balance_before_close + exit_trade_value - trade.commission + position.commission_paid
        
        print(f"✓ Balance before close: ${balance_before_close:.2f}")
        print(f"✓ Exit trade value: ${exit_trade_value:.2f}")
        print(f"✓ Balance after close: ${engine.current_balance:.2f}")
        print(f"✓ Expected balance after close: ${expected_balance_after_close:.2f}")
        
        # The balance should now equal starting balance + realized P&L
        expected_final_balance = 1000.0 + trade.realized_pnl
        print(f"✓ Expected final balance (start + P&L): ${expected_final_balance:.2f}")
        
        if abs(engine.current_balance - expected_final_balance) > 0.01:
            print(f"✗ Final balance mismatch!")
            return False
        
        # Check that total P&L was updated
        print(f"✓ Total P&L before: ${total_pnl_before:.2f}")
        print(f"✓ Total P&L after: ${engine.total_pnl:.2f}")
        print(f"✓ Expected total P&L: ${total_pnl_before + trade.realized_pnl:.2f}")
        
        if abs(engine.total_pnl - (total_pnl_before + trade.realized_pnl)) > 0.01:
            print(f"✗ Total P&L update error!")
            return False
        
        # Final portfolio summary
        final_summary = engine.get_portfolio_summary()
        print(f"✓ Final portfolio value: ${final_summary['total_value']:.2f}")
        print(f"✓ Final current balance: ${final_summary['current_balance']:.2f}")
        print(f"✓ Final unrealized P&L: ${final_summary['unrealized_pnl']:.2f}")
        print(f"✓ Final total P&L: ${final_summary['total_pnl']:.2f}")
        
        # With no open positions, total value should equal current balance
        if abs(final_summary['total_value'] - final_summary['current_balance']) > 0.01:
            print(f"✗ Final total value should equal current balance with no positions!")
            return False
        
        # Unrealized P&L should be zero with no positions
        if abs(final_summary['unrealized_pnl']) > 0.01:
            print(f"✗ Unrealized P&L should be zero with no positions!")
            return False
        
        print("\n🎉 All portfolio accounting tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_positions():
    """Test portfolio accounting with multiple positions"""
    print("\n" + "="*60)
    print("Testing Multiple Positions Accounting...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType
        
        engine = PaperTradingEngine(starting_balance=1000.0)
        pattern_data = {'pattern_strength': 2.5, 'pattern_type': 'bullish_pinbar'}
        
        # Open multiple positions
        positions_data = [
            ('BTCUSDT', 50000.0, 48500.0),
            ('ETHUSDT', 3000.0, 2850.0),
            ('ADAUSDT', 0.5, 0.47)
        ]
        
        total_invested = 0
        total_commissions = 0
        
        for symbol, entry, stop in positions_data:
            success, message, position = engine.open_position(
                symbol=symbol,
                position_type=PositionType.LONG,
                entry_price=entry,
                stop_loss=stop,
                pattern_data=pattern_data
            )
            
            if success:
                trade_amount = position.entry_price * position.quantity
                total_invested += trade_amount
                total_commissions += position.commission_paid
                print(f"✓ Opened {symbol}: ${trade_amount:.2f} invested, ${position.commission_paid:.2f} commission")
            else:
                print(f"✗ Failed to open {symbol}: {message}")
                return False
        
        print(f"✓ Total invested: ${total_invested:.2f}")
        print(f"✓ Total commissions: ${total_commissions:.2f}")
        print(f"✓ Expected remaining balance: ${1000.0 - total_invested - total_commissions:.2f}")
        print(f"✓ Actual remaining balance: ${engine.current_balance:.2f}")
        
        # Check portfolio summary
        summary = engine.get_portfolio_summary()
        total_position_value = sum(pos.current_price * pos.quantity for pos in engine.positions.values())
        expected_total_value = engine.current_balance + total_position_value
        
        print(f"✓ Total position market value: ${total_position_value:.2f}")
        print(f"✓ Portfolio total value: ${summary['total_value']:.2f}")
        print(f"✓ Expected total value: ${expected_total_value:.2f}")
        
        if abs(summary['total_value'] - expected_total_value) > 0.01:
            print(f"✗ Multiple positions total value mismatch!")
            return False
        
        print("✓ Multiple positions accounting test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Multiple positions test failed: {e}")
        return False

def main():
    """Run all portfolio accounting tests"""
    print("="*80)
    print("PORTFOLIO ACCOUNTING FIX - COMPREHENSIVE TEST SUITE")
    print("="*80)
    
    tests = [
        test_portfolio_accounting,
        test_multiple_positions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "="*80)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All portfolio accounting tests passed!")
        print("✓ Position opening correctly deducts full trade amount")
        print("✓ Position closing correctly returns trade value")
        print("✓ Portfolio values calculated accurately")
        print("✓ Current balance represents available cash")
        print("✓ Total value represents cash + market value of positions")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
    
    print("="*80)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
