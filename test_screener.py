"""
Test script for the cryptocurrency screener
"""
import logging
from bybit_api import BybitAPI
from technical_analysis import TechnicalAnalysis
from screener import CryptoScreener

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_api_connection():
    """Test Bybit API connection"""
    logger.info("Testing Bybit API connection...")
    
    api = BybitAPI()
    
    # Test getting trading pairs
    pairs = api.get_perpetual_pairs()
    logger.info(f"Found {len(pairs)} trading pairs")
    
    if pairs:
        # Test getting data for first few pairs
        test_pairs = pairs[:3]
        for symbol in test_pairs:
            logger.info(f"Testing data fetch for {symbol}")
            
            # Test historical data
            df = api.get_kline_data(symbol, limit=10)
            if not df.empty:
                logger.info(f"  ✓ Historical data: {len(df)} candles")
            else:
                logger.warning(f"  ✗ No historical data for {symbol}")
            
            # Test current price
            price = api.get_current_price(symbol)
            if price:
                logger.info(f"  ✓ Current price: ${price}")
            else:
                logger.warning(f"  ✗ No current price for {symbol}")
            
            # Test current candle info
            candle = api.get_current_candle_info(symbol)
            if candle:
                logger.info(f"  ✓ Current candle data available")
            else:
                logger.warning(f"  ✗ No current candle data for {symbol}")
    
    return len(pairs) > 0

def test_technical_analysis():
    """Test technical analysis functions"""
    logger.info("Testing technical analysis functions...")
    
    ta = TechnicalAnalysis()
    
    # Test pinbar detection with sample data
    test_cases = [
        # Bullish pinbar: long lower wick, body in upper part
        {'open': 100, 'high': 105, 'low': 90, 'close': 103, 'expected': 'bullish_pinbar'},
        # Bearish pinbar: long upper wick, body in lower part
        {'open': 100, 'high': 115, 'low': 98, 'close': 99, 'expected': 'bearish_pinbar'},
        # No pinbar: small wicks
        {'open': 100, 'high': 102, 'low': 98, 'close': 101, 'expected': None},
    ]
    
    for i, case in enumerate(test_cases):
        result = ta.is_pinbar(case['open'], case['high'], case['low'], case['close'])
        if result['type'] == case['expected']:
            logger.info(f"  ✓ Test case {i+1}: {result['type'] or 'no pattern'}")
        else:
            logger.warning(f"  ✗ Test case {i+1}: Expected {case['expected']}, got {result['type']}")
    
    # Test long wick detection
    long_wick_result = ta.is_long_wick(100, 110, 95, 102)
    if long_wick_result['has_long_wick']:
        logger.info(f"  ✓ Long wick detection: {len(long_wick_result['patterns'])} patterns found")
    else:
        logger.info(f"  ✓ Long wick detection: No patterns (as expected)")
    
    return True

def test_screener():
    """Test the main screener functionality"""
    logger.info("Testing screener functionality...")
    
    screener = CryptoScreener()
    
    # Test with a small subset of pairs for speed
    logger.info("Testing pattern analysis on a few pairs...")
    
    # Get a few pairs to test
    pairs = screener.get_trading_pairs()[:5]  # Test with first 5 pairs
    
    if not pairs:
        logger.error("No trading pairs available for testing")
        return False
    
    logger.info(f"Testing with pairs: {pairs}")
    
    # Test analyzing individual pairs
    for symbol in pairs:
        try:
            result = screener.analyze_single_pair(symbol, analyze_current=True)
            if result:
                logger.info(f"  ✓ {symbol}: Found pattern - {result['pattern_type']} (strength: {result['pattern_strength']})")
            else:
                logger.info(f"  ✓ {symbol}: No patterns detected")
        except Exception as e:
            logger.error(f"  ✗ {symbol}: Error - {e}")
    
    return True

def main():
    """Run all tests"""
    logger.info("Starting screener tests...")
    
    tests = [
        ("API Connection", test_api_connection),
        ("Technical Analysis", test_technical_analysis),
        ("Screener Functionality", test_screener),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"Test '{test_name}' failed with error: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    for test_name, passed in results.items():
        status = "✓ PASSED" if passed else "✗ FAILED"
        logger.info(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        logger.info("\n🎉 All tests passed! The screener should work correctly.")
        logger.info("You can now run 'python main.py' to start the dashboard.")
    else:
        logger.warning("\n⚠️  Some tests failed. Check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    main()
