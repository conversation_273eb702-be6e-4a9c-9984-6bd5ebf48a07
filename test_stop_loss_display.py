"""
Test script to verify stop loss display in dashboard
"""
import sys
import logging
from datetime import datetime, timezone

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_stop_loss_display():
    """Test stop loss display in console dashboard"""
    print("Testing Stop Loss Display...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType, Position
        from trading_dashboard import TradingDashboard
        from config import TIMEFRAME_CONFIG
        
        # Create trading engine
        engine = PaperTradingEngine(starting_balance=1000.0)
        
        # Create a mock position with stop loss
        mock_position = Position(
            symbol='BTCUSDT',
            position_type=PositionType.LONG,
            entry_price=50000.0,
            quantity=0.02,
            current_price=51000.0,
            stop_loss=48500.0,
            pattern_type='bullish_pinbar',
            pattern_strength=2.5,
            entry_reason='Test position'
        )
        
        # Update unrealized PnL
        mock_position.update_unrealized_pnl(51000.0)
        
        # Add position to engine
        engine.positions[mock_position.id] = mock_position
        
        # Create dashboard
        timeframe_config = TIMEFRAME_CONFIG['4h']
        dashboard = TradingDashboard(timeframe='4h', timeframe_config=timeframe_config)
        
        # Override portfolio manager's trading engine with our mock
        dashboard.portfolio_manager.trading_engine = engine
        
        # Get portfolio status
        portfolio_data = dashboard.portfolio_manager._get_portfolio_status()
        
        # Verify position data includes stop loss
        positions = portfolio_data.get('positions', [])
        if positions:
            position = positions[0]
            print(f"✓ Position found: {position['symbol']}")
            print(f"✓ Entry Price: ${position['entry_price']:.2f}")
            print(f"✓ Current Price: ${position['current_price']:.2f}")
            print(f"✓ Stop Loss: ${position['stop_loss']:.2f}")
            print(f"✓ P&L: ${position['unrealized_pnl']:+.2f}")
            
            # Test console formatting
            console_data = dashboard._format_for_console(portfolio_data)
            console_positions = console_data.get('positions', [])
            
            if console_positions:
                console_pos = console_positions[0]
                print(f"✓ Console format includes stop loss: {console_pos['stop_loss']}")
                
                # Print formatted console output
                print("\n" + "="*80)
                print("MOCK CONSOLE DASHBOARD OUTPUT:")
                print("="*80)
                print(f"{'Symbol':<12} {'Type':<6} {'Entry':<12} {'Current':<12} {'Stop Loss':<12} {'P&L':<12} {'Pattern':<20}")
                print("-" * 100)
                print(f"{console_pos['symbol']:<12} {console_pos['type']:<6} {console_pos['entry']:<12} {console_pos['current']:<12} "
                      f"{console_pos['stop_loss']:<12} {console_pos['pnl']:<12} {console_pos['pattern']:<20}")
                print("="*80)
                
                return True
            else:
                print("✗ Console positions not formatted correctly")
                return False
        else:
            print("✗ No positions found in portfolio data")
            return False
            
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_manager_stop_loss():
    """Test risk manager stop loss detection"""
    print("\nTesting Risk Manager Stop Loss Detection...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType, Position
        from risk_manager import RiskManager
        
        # Create trading engine
        engine = PaperTradingEngine(starting_balance=1000.0)
        
        # Create risk manager
        risk_manager = RiskManager(engine)
        
        # Create a position that should trigger stop loss
        position = Position(
            symbol='BTCUSDT',
            position_type=PositionType.LONG,
            entry_price=50000.0,
            quantity=0.02,
            current_price=48000.0,  # Below stop loss
            stop_loss=48500.0,
            pattern_type='bullish_pinbar',
            pattern_strength=2.5
        )
        
        # Add position to engine
        engine.positions[position.id] = position
        
        # Test stop loss detection
        current_prices = {'BTCUSDT': 48000.0}  # Price below stop loss
        actions = risk_manager.check_all_positions(current_prices)
        
        # Should have one close position action
        if actions:
            action = actions[0]
            if action['action'] == 'close_position' and action['reason'] == 'Stop loss triggered':
                print("✓ Stop loss correctly detected")
                print(f"✓ Action: {action['action']}")
                print(f"✓ Reason: {action['reason']}")
                print(f"✓ Price: ${action['price']:.2f}")
                return True
            else:
                print(f"✗ Unexpected action: {action}")
                return False
        else:
            print("✗ No stop loss action generated")
            return False
            
    except Exception as e:
        print(f"✗ Risk manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run stop loss display tests"""
    print("="*80)
    print("STOP LOSS DISPLAY AND FUNCTIONALITY TEST")
    print("="*80)
    
    tests = [
        test_stop_loss_display,
        test_risk_manager_stop_loss
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "="*80)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All stop loss tests passed!")
        print("✓ Stop loss values are properly tracked")
        print("✓ Stop loss values are displayed in dashboard")
        print("✓ Risk manager correctly detects stop loss triggers")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
    
    print("="*80)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
