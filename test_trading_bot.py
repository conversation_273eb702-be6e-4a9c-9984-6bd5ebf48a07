"""
Test script for the Cryptocurrency Paper Trading Bot
"""
import sys
import logging
from datetime import datetime, timezone

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType
        from trading_signals import SignalGenerator, TradingSignal
        from risk_manager import RiskManager
        from portfolio_manager import PortfolioManager
        from trading_dashboard import TradingDashboard
        from config import TRADING_CONFIG, RISK_CONFIG, SIGNAL_CONFIG
        print("✓ All modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_paper_trading_engine():
    """Test the paper trading engine"""
    print("\nTesting Paper Trading Engine...")
    
    try:
        from paper_trader import PaperTradingEngine, PositionType
        
        # Create engine
        engine = PaperTradingEngine(starting_balance=1000.0)
        
        # Test initial state
        assert engine.current_balance == 1000.0
        assert len(engine.positions) == 0
        assert len(engine.completed_trades) == 0
        
        # Test position size calculation
        quantity, amount = engine.calculate_position_size(100.0, 95.0)  # 5% risk
        assert quantity > 0
        assert amount > 0
        
        # Test portfolio summary
        summary = engine.get_portfolio_summary()
        assert summary['starting_balance'] == 1000.0
        assert summary['current_balance'] == 1000.0
        
        print("✓ Paper trading engine tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Paper trading engine test failed: {e}")
        return False

def test_signal_generation():
    """Test signal generation"""
    print("\nTesting Signal Generation...")
    
    try:
        from trading_signals import SignalGenerator, TradingSignal
        from paper_trader import PositionType
        
        # Create signal generator
        generator = SignalGenerator()
        
        # Create mock pattern data
        mock_patterns = [
            {
                'symbol': 'BTCUSDT',
                'pattern_type': 'bullish_pinbar',
                'pattern_strength': 2.5,
                'close_price': 50000.0,
                'ema_distance': 1.5,
                'turnover_24h': 5000000,
                'is_developing': False,
                'pinbar_analysis': {
                    'is_pinbar': True,
                    'wick_size': 1000.0
                }
            }
        ]
        
        # Generate signals
        signals = generator.generate_signals_from_patterns(mock_patterns)
        
        # Should have at least one signal if pattern is valid
        if signals:
            signal = signals[0]
            assert signal.symbol == 'BTCUSDT'
            assert signal.signal_type == PositionType.LONG
            assert signal.entry_price > 0
            assert signal.stop_loss > 0
        
        print("✓ Signal generation tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Signal generation test failed: {e}")
        return False

def test_risk_manager():
    """Test risk management"""
    print("\nTesting Risk Manager...")
    
    try:
        from paper_trader import PaperTradingEngine
        from risk_manager import RiskManager
        
        # Create engine and risk manager
        engine = PaperTradingEngine(starting_balance=1000.0)
        risk_manager = RiskManager(engine)
        
        # Test with empty positions
        actions = risk_manager.check_all_positions({})
        assert isinstance(actions, list)
        
        # Test daily tracking
        assert risk_manager.daily_pnl == 0.0
        
        print("✓ Risk manager tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Risk manager test failed: {e}")
        return False

def test_portfolio_manager():
    """Test portfolio manager"""
    print("\nTesting Portfolio Manager...")
    
    try:
        from portfolio_manager import PortfolioManager
        from config import TIMEFRAME_CONFIG
        
        # Create portfolio manager
        timeframe_config = TIMEFRAME_CONFIG['4h']
        portfolio = PortfolioManager(timeframe_config=timeframe_config)
        
        # Test initial state
        assert portfolio.trading_engine.current_balance == 1000.0
        assert len(portfolio.active_signals) == 0
        
        # Test portfolio status
        status = portfolio._get_portfolio_status()
        assert 'portfolio_summary' in status
        assert 'positions' in status
        assert 'recent_trades' in status
        
        print("✓ Portfolio manager tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Portfolio manager test failed: {e}")
        return False

def test_configuration():
    """Test configuration settings"""
    print("\nTesting Configuration...")
    
    try:
        from config import TRADING_CONFIG, RISK_CONFIG, SIGNAL_CONFIG, TIMEFRAME_CONFIG
        
        # Test trading config
        assert TRADING_CONFIG['starting_balance'] == 1000.0
        assert TRADING_CONFIG['max_positions'] == 5
        assert TRADING_CONFIG['position_size_percent'] == 10.0
        
        # Test risk config
        assert RISK_CONFIG['max_daily_loss'] == 5.0
        assert RISK_CONFIG['max_drawdown'] == 15.0
        
        # Test signal config
        assert SIGNAL_CONFIG['top_signals_count'] == 5
        assert SIGNAL_CONFIG['min_pattern_strength_for_trade'] == 2.0
        
        # Test timeframe config
        assert '30m' in TIMEFRAME_CONFIG
        assert '4h' in TIMEFRAME_CONFIG
        assert '12h' in TIMEFRAME_CONFIG
        assert '1d' in TIMEFRAME_CONFIG
        
        print("✓ Configuration tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_api_connection():
    """Test API connection"""
    print("\nTesting API Connection...")
    
    try:
        from bybit_api import BybitAPI
        from config import TIMEFRAME_CONFIG
        
        # Create API client
        api = BybitAPI(timeframe_config=TIMEFRAME_CONFIG['4h'])
        
        # Test getting trading pairs (limit to 5 for speed)
        pairs = api.get_perpetual_pairs()
        if pairs:
            print(f"✓ API connection successful - found {len(pairs)} trading pairs")
            
            # Test getting price data for BTCUSDT
            if 'BTCUSDT' in pairs:
                price_data = api.get_current_price_and_volume('BTCUSDT')
                if price_data and 'price' in price_data:
                    print(f"✓ Price data retrieved - BTCUSDT: ${price_data['price']}")
                else:
                    print("⚠ Could not get price data")
            
            return True
        else:
            print("⚠ No trading pairs found - API may be down")
            return False
        
    except Exception as e:
        print(f"✗ API connection test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("="*60)
    print("CRYPTOCURRENCY PAPER TRADING BOT - TEST SUITE")
    print("="*60)
    
    tests = [
        test_imports,
        test_configuration,
        test_paper_trading_engine,
        test_signal_generation,
        test_risk_manager,
        test_portfolio_manager,
        test_api_connection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "="*60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The trading bot is ready to use.")
        print("\nTo start the bot:")
        print("  Web mode:     python main_trader.py")
        print("  Console mode: python main_trader.py --console")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
