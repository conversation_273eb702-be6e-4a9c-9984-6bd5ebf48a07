"""
Test Runner for Trading System
Orchestrates and runs all test suites with comprehensive reporting
"""
import sys
import os
import argparse
import json
from datetime import datetime, timezone
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_integration_tests() -> Dict[str, Any]:
    """Run integration test suite"""
    print("Running Integration Tests...")
    try:
        from test_integration_comprehensive import IntegrationTestSuite
        suite = IntegrationTestSuite()
        return suite.run_all_tests()
    except Exception as e:
        print(f"Error running integration tests: {e}")
        return {'passed': 0, 'total': 1, 'success_rate': 0, 'duration': 0, 'error': str(e)}

def run_performance_tests() -> Dict[str, Any]:
    """Run performance test suite"""
    print("Running Performance Tests...")
    try:
        from test_performance import PerformanceTestSuite
        suite = PerformanceTestSuite()
        return suite.run_all_performance_tests()
    except Exception as e:
        print(f"Error running performance tests: {e}")
        return {'passed': 0, 'total': 1, 'success_rate': 0, 'duration': 0, 'error': str(e)}

def run_existing_tests() -> Dict[str, Any]:
    """Run existing test files"""
    print("Running Existing Tests...")
    
    existing_tests = [
        'test_trading_bot.py',
        'test_stop_loss_display.py',
        'test_portfolio_accounting.py',
        'test_dashboard_with_fix.py'
    ]
    
    results = {
        'passed': 0,
        'total': 0,
        'success_rate': 0,
        'duration': 0,
        'test_results': []
    }
    
    start_time = datetime.now(timezone.utc)
    
    for test_file in existing_tests:
        test_path = os.path.join('..', test_file)
        if os.path.exists(test_path):
            print(f"  Running {test_file}...")
            try:
                # Import and run the test
                import subprocess
                result = subprocess.run([sys.executable, test_path], 
                                      capture_output=True, text=True, timeout=60)
                
                passed = result.returncode == 0
                results['total'] += 1
                if passed:
                    results['passed'] += 1
                
                results['test_results'].append({
                    'test_file': test_file,
                    'passed': passed,
                    'output': result.stdout,
                    'error': result.stderr if result.stderr else None
                })
                
                print(f"    {'PASS' if passed else 'FAIL'}: {test_file}")
                
            except Exception as e:
                results['total'] += 1
                results['test_results'].append({
                    'test_file': test_file,
                    'passed': False,
                    'error': str(e)
                })
                print(f"    ERROR: {test_file} - {e}")
    
    end_time = datetime.now(timezone.utc)
    results['duration'] = (end_time - start_time).total_seconds()
    results['success_rate'] = (results['passed'] / results['total'] * 100) if results['total'] > 0 else 0
    
    return results

def generate_comprehensive_report(integration_results: Dict, performance_results: Dict, 
                                existing_results: Dict) -> Dict[str, Any]:
    """Generate comprehensive test report"""
    
    total_passed = (integration_results.get('passed', 0) + 
                   performance_results.get('summary', {}).get('passed', 0) + 
                   existing_results.get('passed', 0))
    
    total_tests = (integration_results.get('total', 0) + 
                  performance_results.get('summary', {}).get('total', 0) + 
                  existing_results.get('total', 0))
    
    total_duration = (integration_results.get('duration', 0) + 
                     performance_results.get('summary', {}).get('duration', 0) + 
                     existing_results.get('duration', 0))
    
    overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
    
    report = {
        'summary': {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'total_tests': total_tests,
            'total_passed': total_passed,
            'total_failed': total_tests - total_passed,
            'overall_success_rate': overall_success_rate,
            'total_duration': total_duration
        },
        'test_suites': {
            'integration': integration_results,
            'performance': performance_results,
            'existing': existing_results
        },
        'recommendations': []
    }
    
    # Add recommendations based on results
    if overall_success_rate < 100:
        report['recommendations'].append("Some tests failed - review failed tests and fix issues")
    
    if total_duration > 60:
        report['recommendations'].append("Test execution time is high - consider optimizing slow tests")
    
    if integration_results.get('passed', 0) < integration_results.get('total', 1):
        report['recommendations'].append("Integration tests failed - check system component compatibility")
    
    if performance_results.get('summary', {}).get('passed', 0) < performance_results.get('summary', {}).get('total', 1):
        report['recommendations'].append("Performance tests failed - review system performance bottlenecks")
    
    return report

def save_test_report(report: Dict[str, Any], output_file: str = None):
    """Save test report to file"""
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"test_report_{timestamp}.json"
    
    try:
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        print(f"Test report saved to: {output_file}")
    except Exception as e:
        print(f"Error saving test report: {e}")

def print_summary_report(report: Dict[str, Any]):
    """Print summary of test results"""
    summary = report['summary']
    
    print("\n" + "="*80)
    print("COMPREHENSIVE TEST SUITE SUMMARY")
    print("="*80)
    print(f"Timestamp: {summary['timestamp']}")
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['total_passed']}")
    print(f"Failed: {summary['total_failed']}")
    print(f"Success Rate: {summary['overall_success_rate']:.1f}%")
    print(f"Total Duration: {summary['total_duration']:.2f} seconds")
    
    # Print suite breakdown
    print("\nTest Suite Breakdown:")
    suites = report['test_suites']
    
    # Integration tests
    integration = suites['integration']
    print(f"  Integration Tests: {integration.get('passed', 0)}/{integration.get('total', 0)} passed")
    
    # Performance tests
    performance = suites['performance']
    perf_summary = performance.get('summary', {})
    print(f"  Performance Tests: {perf_summary.get('passed', 0)}/{perf_summary.get('total', 0)} passed")
    
    # Existing tests
    existing = suites['existing']
    print(f"  Existing Tests: {existing.get('passed', 0)}/{existing.get('total', 0)} passed")
    
    # Recommendations
    if report['recommendations']:
        print("\nRecommendations:")
        for rec in report['recommendations']:
            print(f"  • {rec}")
    
    # Overall status
    if summary['overall_success_rate'] == 100:
        print("\n🎉 All tests passed! The trading system is functioning correctly.")
    else:
        print(f"\n⚠ {summary['total_failed']} test(s) failed. Please review and fix issues.")
    
    print("="*80)

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description='Trading System Test Runner')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--performance', action='store_true', help='Run performance tests only')
    parser.add_argument('--existing', action='store_true', help='Run existing tests only')
    parser.add_argument('--output', '-o', help='Output file for test report')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # Determine which tests to run
    run_integration = args.integration or not (args.performance or args.existing)
    run_performance = args.performance or not (args.integration or args.existing)
    run_existing = args.existing or not (args.integration or args.performance)
    
    print("="*80)
    print("TRADING SYSTEM COMPREHENSIVE TEST RUNNER")
    print("="*80)
    print(f"Running tests: Integration={run_integration}, Performance={run_performance}, Existing={run_existing}")
    
    # Run selected test suites
    integration_results = {}
    performance_results = {}
    existing_results = {}
    
    if run_integration:
        integration_results = run_integration_tests()
    
    if run_performance:
        performance_results = run_performance_tests()
    
    if run_existing:
        existing_results = run_existing_tests()
    
    # Generate comprehensive report
    report = generate_comprehensive_report(integration_results, performance_results, existing_results)
    
    # Print summary
    print_summary_report(report)
    
    # Save report if requested
    if args.output:
        save_test_report(report, args.output)
    
    # Exit with appropriate code
    success = report['summary']['overall_success_rate'] == 100
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
