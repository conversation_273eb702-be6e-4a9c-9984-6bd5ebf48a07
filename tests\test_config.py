"""
Test Configuration and Utilities
Provides common test fixtures, mock data, and utility functions
"""
import os
import sys
from datetime import datetime, timezone
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestConfig:
    """Test configuration and constants"""
    
    # Test trading parameters
    TEST_STARTING_BALANCE = 1000.0
    TEST_POSITION_SIZE = 100.0
    TEST_COMMISSION_RATE = 0.001  # 0.1%
    
    # Test symbols and prices
    TEST_SYMBOLS = {
        'BTCUSDT': {
            'entry_price': 50000.0,
            'stop_loss': 48500.0,
            'take_profit': 52500.0,
            'current_price': 50000.0
        },
        'ETHUSDT': {
            'entry_price': 3000.0,
            'stop_loss': 2850.0,
            'take_profit': 3300.0,
            'current_price': 3000.0
        },
        'ADAUSDT': {
            'entry_price': 0.50,
            'stop_loss': 0.47,
            'take_profit': 0.55,
            'current_price': 0.50
        }
    }
    
    # Test timeframes
    TEST_TIMEFRAMES = {
        '30m': {
            'interval_minutes': 30,
            'bybit_interval': '30',
            'update_interval': 15,
            'candle_check_interval': 30,
            'display_name': '30-Minute',
            'short_name': '30M'
        },
        '4h': {
            'interval_minutes': 240,
            'bybit_interval': '240',
            'update_interval': 30,
            'candle_check_interval': 60,
            'display_name': '4-Hour',
            'short_name': '4H'
        }
    }

class MockDataGenerator:
    """Generates mock data for testing"""
    
    @staticmethod
    def create_mock_pattern(symbol: str = 'BTCUSDT', pattern_type: str = 'bullish_pinbar') -> Dict[str, Any]:
        """Create mock pattern data"""
        base_price = TestConfig.TEST_SYMBOLS.get(symbol, TestConfig.TEST_SYMBOLS['BTCUSDT'])
        
        return {
            'symbol': symbol,
            'pattern_type': pattern_type,
            'pattern_strength': 2.5,
            'close_price': base_price['entry_price'],
            'ema_distance': 1.5,
            'turnover_24h': 5000000,
            'is_developing': False,
            'pinbar_analysis': {
                'is_pinbar': True,
                'wick_size': abs(base_price['entry_price'] - base_price['stop_loss']),
                'body_size': base_price['entry_price'] * 0.01,
                'wick_ratio': 3.0
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    @staticmethod
    def create_mock_price_data(symbol: str = 'BTCUSDT', price: float = None) -> Dict[str, Any]:
        """Create mock price data"""
        if price is None:
            price = TestConfig.TEST_SYMBOLS.get(symbol, TestConfig.TEST_SYMBOLS['BTCUSDT'])['current_price']
        
        return {
            'symbol': symbol,
            'price': price,
            'volume_24h': 1000000,
            'price_change_24h': 0.02,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    @staticmethod
    def create_mock_candle_data(symbol: str = 'BTCUSDT', timeframe: str = '4h') -> List[Dict[str, Any]]:
        """Create mock candle data"""
        base_price = TestConfig.TEST_SYMBOLS.get(symbol, TestConfig.TEST_SYMBOLS['BTCUSDT'])['entry_price']
        
        candles = []
        for i in range(100):  # 100 candles of historical data
            price_variation = (i % 10 - 5) * 100  # Simple price variation
            candle = {
                'timestamp': datetime.now(timezone.utc).timestamp() - (i * 3600 * 4),  # 4h intervals
                'open': base_price + price_variation,
                'high': base_price + price_variation + 200,
                'low': base_price + price_variation - 200,
                'close': base_price + price_variation + 50,
                'volume': 1000 + (i % 100) * 10
            }
            candles.append(candle)
        
        return list(reversed(candles))  # Most recent first

class TestUtilities:
    """Utility functions for testing"""
    
    @staticmethod
    def assert_portfolio_balance(engine, expected_balance: float, tolerance: float = 0.01):
        """Assert portfolio balance within tolerance"""
        actual_balance = engine.current_balance
        assert abs(actual_balance - expected_balance) <= tolerance, \
            f"Balance mismatch: expected {expected_balance}, got {actual_balance}"
    
    @staticmethod
    def assert_position_count(engine, expected_count: int):
        """Assert number of open positions"""
        actual_count = len(engine.positions)
        assert actual_count == expected_count, \
            f"Position count mismatch: expected {expected_count}, got {actual_count}"
    
    @staticmethod
    def assert_trade_count(engine, expected_count: int):
        """Assert number of completed trades"""
        actual_count = len(engine.completed_trades)
        assert actual_count == expected_count, \
            f"Trade count mismatch: expected {expected_count}, got {actual_count}"
    
    @staticmethod
    def create_test_position(engine, symbol: str = 'BTCUSDT', position_type=None):
        """Create a test position"""
        if position_type is None:
            from paper_trader import PositionType
            position_type = PositionType.LONG
        
        symbol_data = TestConfig.TEST_SYMBOLS.get(symbol, TestConfig.TEST_SYMBOLS['BTCUSDT'])
        pattern_data = MockDataGenerator.create_mock_pattern(symbol)
        
        success, message, position = engine.open_position(
            symbol=symbol,
            position_type=position_type,
            entry_price=symbol_data['entry_price'],
            stop_loss=symbol_data['stop_loss'],
            pattern_data=pattern_data
        )
        
        assert success, f"Failed to create test position: {message}"
        return position
    
    @staticmethod
    def simulate_price_movement(engine, symbol: str, new_price: float):
        """Simulate price movement for testing"""
        price_data = {symbol: new_price}
        engine.update_positions(price_data)
        return price_data
    
    @staticmethod
    def get_position_by_symbol(engine, symbol: str):
        """Get position by symbol"""
        for position in engine.positions.values():
            if position.symbol == symbol:
                return position
        return None
    
    @staticmethod
    def calculate_expected_pnl(entry_price: float, exit_price: float, quantity: float, 
                             position_type, commission: float = 0.0):
        """Calculate expected P&L for validation"""
        from paper_trader import PositionType
        
        if position_type == PositionType.LONG:
            gross_pnl = (exit_price - entry_price) * quantity
        else:
            gross_pnl = (entry_price - exit_price) * quantity
        
        return gross_pnl - commission

class TestReporter:
    """Test reporting utilities"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = None
        self.end_time = None
    
    def start_test_session(self):
        """Start a test session"""
        self.start_time = datetime.now(timezone.utc)
        self.test_results = []
    
    def end_test_session(self):
        """End a test session"""
        self.end_time = datetime.now(timezone.utc)
    
    def record_test(self, test_name: str, passed: bool, message: str = "", 
                   execution_time: float = 0.0):
        """Record a test result"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'message': message,
            'execution_time': execution_time,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        self.test_results.append(result)
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate test report"""
        if not self.start_time or not self.end_time:
            raise ValueError("Test session not properly started/ended")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        duration = (self.end_time - self.start_time).total_seconds()
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'duration': duration
            },
            'results': self.test_results,
            'session': {
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat()
            }
        }
    
    def print_summary(self):
        """Print test summary"""
        report = self.generate_report()
        summary = report['summary']
        
        print("\n" + "="*60)
        print("TEST SESSION SUMMARY")
        print("="*60)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']}")
        print(f"Failed: {summary['failed']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Duration: {summary['duration']:.2f} seconds")
        
        if summary['failed'] > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if not result['passed']:
                    print(f"  ❌ {result['test_name']}: {result['message']}")
        
        print("="*60)

# Export commonly used items
__all__ = [
    'TestConfig',
    'MockDataGenerator', 
    'TestUtilities',
    'TestReporter'
]
