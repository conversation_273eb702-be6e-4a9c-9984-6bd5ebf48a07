"""
Comprehensive Integration Test Suite
Tests the complete trading system workflow from signal generation to position closure
"""
import sys
import os
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegrationTestSuite:
    """Comprehensive integration test suite for the trading system"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = None
        self.end_time = None
    
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        self.test_results.append(result)
        print(f"[{status}] {test_name}: {message}")
        return passed
    
    def test_system_imports(self) -> bool:
        """Test that all system components can be imported"""
        try:
            from paper_trader import PaperTradingEngine, PositionType, Position, Trade
            from trading_signals import SignalGenerator, TradingSignal
            from risk_manager import RiskManager
            from portfolio_manager import PortfolioManager
            from trading_dashboard import TradingDashboard
            from config import TRADING_CONFIG, RISK_CONFIG, SIGNAL_CONFIG, TIMEFRAME_CONFIG
            from bybit_api import BybitAPI
            from screener import CryptoScreener
            from technical_analysis import TechnicalAnalysis
            
            return self.log_test_result("System Imports", True, "All modules imported successfully")
        except ImportError as e:
            return self.log_test_result("System Imports", False, f"Import error: {e}")
    
    def test_configuration_integrity(self) -> bool:
        """Test that all configuration values are valid"""
        try:
            from config import TRADING_CONFIG, RISK_CONFIG, SIGNAL_CONFIG, TIMEFRAME_CONFIG
            
            # Test trading config
            assert TRADING_CONFIG['starting_balance'] > 0
            assert TRADING_CONFIG['max_positions'] > 0
            assert 0 < TRADING_CONFIG['position_size_percent'] <= 100
            
            # Test risk config
            assert 0 < RISK_CONFIG['max_daily_loss'] <= 100
            assert 0 < RISK_CONFIG['max_drawdown'] <= 100
            assert len(RISK_CONFIG['take_profit_ratios']) > 0
            
            # Test signal config
            assert SIGNAL_CONFIG['min_pattern_strength_for_trade'] > 0
            assert SIGNAL_CONFIG['top_signals_count'] > 0
            
            # Test timeframe config
            for tf, config in TIMEFRAME_CONFIG.items():
                assert config['interval_minutes'] > 0
                assert config['update_interval'] > 0
                assert 'display_name' in config
            
            return self.log_test_result("Configuration Integrity", True, "All config values valid")
        except (AssertionError, KeyError) as e:
            return self.log_test_result("Configuration Integrity", False, f"Config error: {e}")
    
    def test_trading_engine_lifecycle(self) -> bool:
        """Test complete trading engine lifecycle"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            
            # Initialize engine
            engine = PaperTradingEngine(starting_balance=1000.0)
            
            # Test initial state
            assert engine.current_balance == 1000.0
            assert len(engine.positions) == 0
            assert engine.total_pnl == 0.0
            
            # Test position opening
            pattern_data = {'pattern_strength': 2.5, 'pattern_type': 'bullish_pinbar'}
            success, message, position = engine.open_position(
                symbol='BTCUSDT',
                position_type=PositionType.LONG,
                entry_price=50000.0,
                stop_loss=48500.0,
                pattern_data=pattern_data
            )
            
            assert success, f"Position opening failed: {message}"
            assert len(engine.positions) == 1
            assert engine.current_balance < 1000.0  # Should have deducted trade amount
            
            # Test position updates
            engine.update_positions({'BTCUSDT': 51000.0})
            updated_position = list(engine.positions.values())[0]
            assert updated_position.current_price == 51000.0
            assert updated_position.unrealized_pnl > 0  # Should be profitable
            
            # Test position closing
            position_id = list(engine.positions.keys())[0]
            success, message, trade = engine.close_position(position_id, 51000.0, "Test close")
            
            assert success, f"Position closing failed: {message}"
            assert len(engine.positions) == 0
            assert trade.realized_pnl > 0  # Should be profitable
            assert engine.total_pnl == trade.realized_pnl
            
            return self.log_test_result("Trading Engine Lifecycle", True, "Complete lifecycle tested successfully")
        except Exception as e:
            return self.log_test_result("Trading Engine Lifecycle", False, f"Error: {e}")
    
    def test_risk_management_integration(self) -> bool:
        """Test risk management system integration"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            from risk_manager import RiskManager
            
            engine = PaperTradingEngine(starting_balance=1000.0)
            risk_manager = RiskManager(engine)
            
            # Open position
            pattern_data = {'pattern_strength': 2.5, 'pattern_type': 'bullish_pinbar'}
            success, _, position = engine.open_position(
                symbol='BTCUSDT',
                position_type=PositionType.LONG,
                entry_price=50000.0,
                stop_loss=48500.0,
                pattern_data=pattern_data
            )
            assert success
            
            # Test stop loss detection
            current_prices = {'BTCUSDT': 48000.0}  # Below stop loss
            actions = risk_manager.check_all_positions(current_prices)
            
            assert len(actions) > 0, "Stop loss should have been triggered"
            assert actions[0]['action'] == 'close_position'
            assert actions[0]['reason'] == 'Stop loss triggered'
            
            # Test action execution
            results = risk_manager.execute_risk_actions(actions, current_prices)
            assert len(results) > 0, "Risk actions should have been executed"
            assert len(engine.positions) == 0, "Position should have been closed"
            
            return self.log_test_result("Risk Management Integration", True, "Risk management working correctly")
        except Exception as e:
            return self.log_test_result("Risk Management Integration", False, f"Error: {e}")
    
    def test_portfolio_manager_integration(self) -> bool:
        """Test portfolio manager integration"""
        try:
            from portfolio_manager import PortfolioManager
            from config import TIMEFRAME_CONFIG
            
            # Initialize portfolio manager
            timeframe_config = TIMEFRAME_CONFIG['4h']
            portfolio = PortfolioManager(timeframe_config=timeframe_config)
            
            # Test initial state
            assert portfolio.trading_engine.current_balance == 1000.0
            assert len(portfolio.active_signals) == 0
            
            # Test portfolio status
            status = portfolio._get_portfolio_status()
            assert 'portfolio_summary' in status
            assert 'positions' in status
            assert 'recent_trades' in status
            assert 'active_signals' in status
            
            # Verify portfolio summary structure
            summary = status['portfolio_summary']
            required_fields = [
                'starting_balance', 'current_balance', 'total_value',
                'total_pnl', 'unrealized_pnl', 'daily_pnl',
                'open_positions', 'total_trades', 'win_rate'
            ]
            for field in required_fields:
                assert field in summary, f"Missing field: {field}"
            
            return self.log_test_result("Portfolio Manager Integration", True, "Portfolio manager working correctly")
        except Exception as e:
            return self.log_test_result("Portfolio Manager Integration", False, f"Error: {e}")
    
    def test_dashboard_integration(self) -> bool:
        """Test dashboard integration"""
        try:
            from trading_dashboard import TradingDashboard
            from config import TIMEFRAME_CONFIG
            
            # Initialize dashboard
            timeframe_config = TIMEFRAME_CONFIG['4h']
            dashboard = TradingDashboard(timeframe='4h', timeframe_config=timeframe_config)
            
            # Test dashboard data structure
            assert hasattr(dashboard, 'portfolio_manager')
            assert hasattr(dashboard, 'app')
            assert dashboard.timeframe == '4h'
            
            # Test console formatting with empty data
            empty_data = {
                'portfolio_summary': {
                    'current_balance': 1000.0,
                    'total_value': 1000.0,
                    'total_pnl': 0.0,
                    'daily_pnl': 0.0,
                    'unrealized_pnl': 0.0,
                    'win_rate': 0.0,
                    'open_positions': 0,
                    'total_trades': 0,
                    'max_drawdown': 0.0,
                    'allocation_percent': 0.0
                },
                'positions': [],
                'recent_trades': []
            }
            
            console_data = dashboard._format_for_console(empty_data)
            assert 'summary' in console_data
            assert 'positions' in console_data
            assert 'recent_trades' in console_data
            
            return self.log_test_result("Dashboard Integration", True, "Dashboard integration working correctly")
        except Exception as e:
            return self.log_test_result("Dashboard Integration", False, f"Error: {e}")
    
    def test_signal_generation_integration(self) -> bool:
        """Test signal generation integration"""
        try:
            from trading_signals import SignalGenerator
            from paper_trader import PositionType
            
            generator = SignalGenerator()
            
            # Test with mock pattern data
            mock_patterns = [
                {
                    'symbol': 'BTCUSDT',
                    'pattern_type': 'bullish_pinbar',
                    'pattern_strength': 2.5,
                    'close_price': 50000.0,
                    'ema_distance': 1.5,
                    'turnover_24h': 5000000,
                    'is_developing': False,
                    'pinbar_analysis': {
                        'is_pinbar': True,
                        'wick_size': 1000.0
                    }
                }
            ]
            
            signals = generator.generate_signals_from_patterns(mock_patterns)
            
            if signals:
                signal = signals[0]
                assert signal.symbol == 'BTCUSDT'
                assert signal.signal_type == PositionType.LONG
                assert signal.entry_price > 0
                assert signal.stop_loss > 0
                assert signal.confidence > 0
            
            return self.log_test_result("Signal Generation Integration", True, "Signal generation working correctly")
        except Exception as e:
            return self.log_test_result("Signal Generation Integration", False, f"Error: {e}")
    
    def run_all_tests(self) -> Dict:
        """Run all integration tests"""
        self.start_time = datetime.now(timezone.utc)
        
        print("="*80)
        print("COMPREHENSIVE INTEGRATION TEST SUITE")
        print("="*80)
        
        # Define test sequence
        tests = [
            self.test_system_imports,
            self.test_configuration_integrity,
            self.test_trading_engine_lifecycle,
            self.test_risk_management_integration,
            self.test_portfolio_manager_integration,
            self.test_dashboard_integration,
            self.test_signal_generation_integration
        ]
        
        # Run tests
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                self.log_test_result(test.__name__, False, f"Unexpected error: {e}")
        
        self.end_time = datetime.now(timezone.utc)
        duration = (self.end_time - self.start_time).total_seconds()
        
        # Generate summary
        print("\n" + "="*80)
        print(f"TEST RESULTS: {passed}/{total} tests passed")
        print(f"Duration: {duration:.2f} seconds")
        print("="*80)
        
        if passed == total:
            print("🎉 All integration tests passed!")
            print("✓ System components properly integrated")
            print("✓ Trading workflow functioning correctly")
            print("✓ Risk management operational")
            print("✓ Dashboard integration working")
        else:
            print("⚠ Some integration tests failed")
            print("Please review failed tests above")
        
        return {
            'passed': passed,
            'total': total,
            'success_rate': (passed / total) * 100,
            'duration': duration,
            'results': self.test_results
        }

def main():
    """Run the comprehensive integration test suite"""
    suite = IntegrationTestSuite()
    results = suite.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if results['passed'] == results['total'] else 1)

if __name__ == "__main__":
    main()
