"""
Performance Test Suite
Tests system performance under various load conditions
"""
import sys
import os
import time
import threading
import concurrent.futures
from datetime import datetime, timezone
from typing import Dict, List

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig, MockDataGenerator, TestUtilities, TestReporter

class PerformanceTestSuite:
    """Performance testing for the trading system"""
    
    def __init__(self):
        self.reporter = TestReporter()
        self.performance_metrics = {}
    
    def measure_execution_time(self, func, *args, **kwargs):
        """Measure function execution time"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
    
    def test_position_opening_performance(self) -> bool:
        """Test performance of opening multiple positions"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            
            engine = PaperTradingEngine(starting_balance=10000.0)  # Higher balance for multiple positions
            pattern_data = MockDataGenerator.create_mock_pattern()
            
            # Test opening 100 positions
            num_positions = 100
            start_time = time.time()
            
            successful_opens = 0
            for i in range(num_positions):
                symbol = f"TEST{i:03d}USDT"
                success, _, _ = engine.open_position(
                    symbol=symbol,
                    position_type=PositionType.LONG,
                    entry_price=50000.0 + i,  # Vary prices slightly
                    stop_loss=48500.0 + i,
                    pattern_data=pattern_data
                )
                if success:
                    successful_opens += 1
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_position = total_time / num_positions
            
            self.performance_metrics['position_opening'] = {
                'total_positions': num_positions,
                'successful_opens': successful_opens,
                'total_time': total_time,
                'avg_time_per_position': avg_time_per_position,
                'positions_per_second': num_positions / total_time
            }
            
            # Performance criteria: should open positions in reasonable time
            acceptable_time_per_position = 0.01  # 10ms per position
            performance_ok = avg_time_per_position < acceptable_time_per_position
            
            message = f"Opened {successful_opens}/{num_positions} positions in {total_time:.3f}s (avg: {avg_time_per_position:.4f}s/position)"
            self.reporter.record_test("Position Opening Performance", performance_ok, message, total_time)
            
            return performance_ok
            
        except Exception as e:
            self.reporter.record_test("Position Opening Performance", False, f"Error: {e}")
            return False
    
    def test_price_update_performance(self) -> bool:
        """Test performance of updating position prices"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            
            engine = PaperTradingEngine(starting_balance=10000.0)
            pattern_data = MockDataGenerator.create_mock_pattern()
            
            # Create multiple positions
            num_positions = 50
            symbols = []
            for i in range(num_positions):
                symbol = f"TEST{i:03d}USDT"
                symbols.append(symbol)
                engine.open_position(
                    symbol=symbol,
                    position_type=PositionType.LONG,
                    entry_price=50000.0 + i,
                    stop_loss=48500.0 + i,
                    pattern_data=pattern_data
                )
            
            # Test price updates
            num_updates = 1000
            start_time = time.time()
            
            for update_round in range(num_updates):
                price_data = {}
                for i, symbol in enumerate(symbols):
                    # Simulate price changes
                    price_data[symbol] = 50000.0 + i + (update_round % 100)
                
                engine.update_positions(price_data)
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_update = total_time / num_updates
            
            self.performance_metrics['price_updates'] = {
                'num_positions': num_positions,
                'num_updates': num_updates,
                'total_time': total_time,
                'avg_time_per_update': avg_time_per_update,
                'updates_per_second': num_updates / total_time
            }
            
            # Performance criteria: should update prices quickly
            acceptable_time_per_update = 0.005  # 5ms per update cycle
            performance_ok = avg_time_per_update < acceptable_time_per_update
            
            message = f"Updated {num_positions} positions {num_updates} times in {total_time:.3f}s (avg: {avg_time_per_update:.4f}s/update)"
            self.reporter.record_test("Price Update Performance", performance_ok, message, total_time)
            
            return performance_ok
            
        except Exception as e:
            self.reporter.record_test("Price Update Performance", False, f"Error: {e}")
            return False
    
    def test_risk_management_performance(self) -> bool:
        """Test performance of risk management checks"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            from risk_manager import RiskManager
            
            engine = PaperTradingEngine(starting_balance=10000.0)
            risk_manager = RiskManager(engine)
            pattern_data = MockDataGenerator.create_mock_pattern()
            
            # Create positions
            num_positions = 30
            symbols = []
            for i in range(num_positions):
                symbol = f"TEST{i:03d}USDT"
                symbols.append(symbol)
                engine.open_position(
                    symbol=symbol,
                    position_type=PositionType.LONG,
                    entry_price=50000.0 + i,
                    stop_loss=48500.0 + i,
                    pattern_data=pattern_data
                )
            
            # Test risk checks
            num_checks = 500
            start_time = time.time()
            
            for check_round in range(num_checks):
                price_data = {}
                for i, symbol in enumerate(symbols):
                    # Simulate various price scenarios
                    base_price = 50000.0 + i
                    if check_round % 10 == 0:
                        # Occasionally trigger stop loss
                        price_data[symbol] = base_price - 2000
                    else:
                        price_data[symbol] = base_price + (check_round % 50)
                
                actions = risk_manager.check_all_positions(price_data)
                if actions:
                    # Execute actions if any
                    risk_manager.execute_risk_actions(actions, price_data)
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_check = total_time / num_checks
            
            self.performance_metrics['risk_management'] = {
                'num_positions': num_positions,
                'num_checks': num_checks,
                'total_time': total_time,
                'avg_time_per_check': avg_time_per_check,
                'checks_per_second': num_checks / total_time
            }
            
            # Performance criteria: risk checks should be fast
            acceptable_time_per_check = 0.01  # 10ms per risk check cycle
            performance_ok = avg_time_per_check < acceptable_time_per_check
            
            message = f"Performed {num_checks} risk checks on {num_positions} positions in {total_time:.3f}s (avg: {avg_time_per_check:.4f}s/check)"
            self.reporter.record_test("Risk Management Performance", performance_ok, message, total_time)
            
            return performance_ok
            
        except Exception as e:
            self.reporter.record_test("Risk Management Performance", False, f"Error: {e}")
            return False
    
    def test_portfolio_calculation_performance(self) -> bool:
        """Test performance of portfolio summary calculations"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            
            engine = PaperTradingEngine(starting_balance=10000.0)
            pattern_data = MockDataGenerator.create_mock_pattern()
            
            # Create positions and trades
            num_positions = 25
            for i in range(num_positions):
                symbol = f"TEST{i:03d}USDT"
                engine.open_position(
                    symbol=symbol,
                    position_type=PositionType.LONG,
                    entry_price=50000.0 + i,
                    stop_loss=48500.0 + i,
                    pattern_data=pattern_data
                )
            
            # Close some positions to create trade history
            positions_to_close = list(engine.positions.keys())[:10]
            for pos_id in positions_to_close:
                engine.close_position(pos_id, 51000.0, "Performance test")
            
            # Test portfolio summary calculations
            num_calculations = 1000
            start_time = time.time()
            
            for _ in range(num_calculations):
                summary = engine.get_portfolio_summary()
                # Verify summary has required fields
                assert 'current_balance' in summary
                assert 'total_value' in summary
                assert 'total_pnl' in summary
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time_per_calculation = total_time / num_calculations
            
            self.performance_metrics['portfolio_calculations'] = {
                'num_positions': len(engine.positions),
                'num_trades': len(engine.completed_trades),
                'num_calculations': num_calculations,
                'total_time': total_time,
                'avg_time_per_calculation': avg_time_per_calculation,
                'calculations_per_second': num_calculations / total_time
            }
            
            # Performance criteria: portfolio calculations should be fast
            acceptable_time_per_calculation = 0.001  # 1ms per calculation
            performance_ok = avg_time_per_calculation < acceptable_time_per_calculation
            
            message = f"Performed {num_calculations} portfolio calculations in {total_time:.3f}s (avg: {avg_time_per_calculation:.4f}s/calc)"
            self.reporter.record_test("Portfolio Calculation Performance", performance_ok, message, total_time)
            
            return performance_ok
            
        except Exception as e:
            self.reporter.record_test("Portfolio Calculation Performance", False, f"Error: {e}")
            return False
    
    def test_concurrent_operations(self) -> bool:
        """Test performance under concurrent operations"""
        try:
            from paper_trader import PaperTradingEngine, PositionType
            from portfolio_manager import PortfolioManager
            from config import TIMEFRAME_CONFIG
            
            # Create portfolio manager
            timeframe_config = TIMEFRAME_CONFIG['4h']
            portfolio = PortfolioManager(timeframe_config=timeframe_config)
            
            def simulate_portfolio_updates():
                """Simulate portfolio update operations"""
                for _ in range(10):
                    portfolio._get_portfolio_status()
                    time.sleep(0.01)  # Small delay to simulate real conditions
            
            def simulate_price_updates():
                """Simulate price update operations"""
                for i in range(10):
                    price_data = {'BTCUSDT': 50000 + i * 100}
                    portfolio.trading_engine.update_positions(price_data)
                    time.sleep(0.01)
            
            # Test concurrent operations
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                
                # Submit multiple concurrent operations
                for _ in range(3):
                    futures.append(executor.submit(simulate_portfolio_updates))
                    futures.append(executor.submit(simulate_price_updates))
                
                # Wait for all operations to complete
                concurrent.futures.wait(futures)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            self.performance_metrics['concurrent_operations'] = {
                'num_threads': 6,
                'total_time': total_time,
                'operations_completed': len(futures)
            }
            
            # Performance criteria: concurrent operations should complete without errors
            performance_ok = total_time < 5.0  # Should complete within 5 seconds
            
            message = f"Completed {len(futures)} concurrent operations in {total_time:.3f}s"
            self.reporter.record_test("Concurrent Operations Performance", performance_ok, message, total_time)
            
            return performance_ok
            
        except Exception as e:
            self.reporter.record_test("Concurrent Operations Performance", False, f"Error: {e}")
            return False
    
    def run_all_performance_tests(self) -> Dict:
        """Run all performance tests"""
        self.reporter.start_test_session()
        
        print("="*80)
        print("PERFORMANCE TEST SUITE")
        print("="*80)
        
        tests = [
            self.test_position_opening_performance,
            self.test_price_update_performance,
            self.test_risk_management_performance,
            self.test_portfolio_calculation_performance,
            self.test_concurrent_operations
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                self.reporter.record_test(test.__name__, False, f"Unexpected error: {e}")
        
        self.reporter.end_test_session()
        
        # Print performance metrics
        print("\n" + "="*80)
        print("PERFORMANCE METRICS")
        print("="*80)
        
        for test_name, metrics in self.performance_metrics.items():
            print(f"\n{test_name.replace('_', ' ').title()}:")
            for key, value in metrics.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")
        
        self.reporter.print_summary()
        
        return self.reporter.generate_report()

def main():
    """Run the performance test suite"""
    suite = PerformanceTestSuite()
    results = suite.run_all_performance_tests()
    
    # Exit with appropriate code
    success = results['summary']['passed'] == results['summary']['total']
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
