"""
Enhanced Trading Dashboard for Cryptocurrency Trading Bot
Provides real-time monitoring of portfolio, positions, and performance
"""
from flask import Flask, render_template, jsonify
import threading
import time
import schedule
from datetime import datetime, timezone
import logging
import json

from portfolio_manager import PortfolioManager
from config import TIMEFRAME_CONFIG

logger = logging.getLogger(__name__)

class TradingDashboard:
    """Enhanced dashboard for paper trading bot"""
    
    def __init__(self, port: int = 5000, timeframe: str = '4h', timeframe_config: dict = None):
        self.app = Flask(__name__)
        self.timeframe = timeframe
        self.timeframe_config = timeframe_config or TIMEFRAME_CONFIG.get(timeframe, TIMEFRAME_CONFIG['4h'])
        self.portfolio_manager = PortfolioManager(timeframe_config=self.timeframe_config)
        self.port = port
        self.running = False
        
        # Dashboard data cache
        self.dashboard_data = {}
        self.last_update = None
        
        # Setup routes
        self.setup_routes()
        
        # Background scheduler
        self.scheduler_thread = None
        
        logger.info(f"Trading dashboard initialized for {self.timeframe_config['display_name']} timeframe")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main trading dashboard page"""
            return render_template('trading_dashboard.html',
                                 timeframe=self.timeframe,
                                 timeframe_config=self.timeframe_config)
        
        @self.app.route('/api/portfolio')
        def get_portfolio_data():
            """API endpoint for portfolio data"""
            try:
                return jsonify(self.dashboard_data)
            except Exception as e:
                logger.error(f"Error getting portfolio data: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/performance')
        def get_performance_data():
            """API endpoint for performance metrics"""
            try:
                metrics = self.portfolio_manager.get_performance_metrics()
                return jsonify(metrics)
            except Exception as e:
                logger.error(f"Error getting performance data: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/refresh')
        def refresh_portfolio():
            """API endpoint to manually refresh portfolio"""
            try:
                self.update_portfolio_job()
                return jsonify({'status': 'success', 'message': 'Portfolio refreshed'})
            except Exception as e:
                logger.error(f"Error refreshing portfolio: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/close_position/<position_id>')
        def close_position(position_id):
            """API endpoint to manually close a position"""
            try:
                # Get current price for the position
                position = self.portfolio_manager.trading_engine.positions.get(position_id)
                if not position:
                    return jsonify({'error': 'Position not found'}), 404
                
                price_data = self.portfolio_manager.api.get_current_price_and_volume(position.symbol)
                if not price_data or 'price' not in price_data:
                    return jsonify({'error': 'Could not get current price'}), 400
                
                current_price = price_data['price']
                success, message, trade = self.portfolio_manager.trading_engine.close_position(
                    position_id, current_price, "Manual close via dashboard"
                )
                
                if success:
                    return jsonify({'status': 'success', 'message': message})
                else:
                    return jsonify({'error': message}), 400
                    
            except Exception as e:
                logger.error(f"Error closing position {position_id}: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/console')
        def get_console_data():
            """API endpoint for console/terminal dashboard data"""
            try:
                data = self.dashboard_data
                if not data:
                    return jsonify({'error': 'No data available'})
                
                # Format for console display
                console_data = self._format_for_console(data)
                return jsonify(console_data)
                
            except Exception as e:
                logger.error(f"Error getting console data: {e}")
                return jsonify({'error': str(e)}), 500
    
    def _format_for_console(self, data: dict) -> dict:
        """Format data for console/terminal display"""
        if 'portfolio_summary' not in data:
            return {'error': 'Invalid data format'}
        
        summary = data['portfolio_summary']
        positions = data.get('positions', [])
        recent_trades = data.get('recent_trades', [])
        
        # Format portfolio summary
        console_summary = {
            'balance': f"${summary['current_balance']:,.2f}",
            'total_value': f"${summary['total_value']:,.2f}",
            'total_pnl': f"${summary['total_pnl']:+,.2f}",
            'daily_pnl': f"${summary['daily_pnl']:+,.2f}",
            'unrealized_pnl': f"${summary['unrealized_pnl']:+,.2f}",
            'win_rate': f"{summary['win_rate']:.1f}%",
            'open_positions': summary['open_positions'],
            'total_trades': summary['total_trades'],
            'max_drawdown': f"{summary['max_drawdown']:.2f}%",
            'allocation': f"{summary['allocation_percent']:.1f}%"
        }
        
        # Format positions
        console_positions = []
        for pos in positions:
            pnl_color = 'green' if pos['unrealized_pnl'] >= 0 else 'red'
            console_positions.append({
                'symbol': pos['symbol'],
                'type': pos['type'].upper(),
                'entry': f"${pos['entry_price']:.4f}",
                'current': f"${pos['current_price']:.4f}",
                'stop_loss': f"${pos['stop_loss']:.4f}",
                'pnl': f"${pos['unrealized_pnl']:+,.2f}",
                'pnl_color': pnl_color,
                'pattern': pos['pattern_type'].replace('_', ' ').title(),
                'strength': f"{pos['pattern_strength']:.1f}x"
            })
        
        # Format recent trades
        console_trades = []
        for trade in recent_trades[-5:]:  # Last 5 trades
            pnl_color = 'green' if trade['realized_pnl'] >= 0 else 'red'
            console_trades.append({
                'symbol': trade['symbol'],
                'type': trade['type'].upper(),
                'pnl': f"${trade['realized_pnl']:+,.2f}",
                'pnl_color': pnl_color,
                'rr_ratio': f"{trade['risk_reward_ratio']:.2f}",
                'hold_time': f"{trade['hold_time_minutes']:.0f}m",
                'exit_reason': trade['exit_reason']
            })
        
        return {
            'summary': console_summary,
            'positions': console_positions,
            'recent_trades': console_trades,
            'last_update': data.get('last_update', ''),
            'timeframe': data.get('timeframe', '')
        }
    
    def update_portfolio_job(self):
        """Job to update portfolio data"""
        try:
            logger.info("Updating portfolio...")
            self.dashboard_data = self.portfolio_manager.update_portfolio()
            self.last_update = datetime.now(timezone.utc)
            
            # Log summary
            if 'portfolio_summary' in self.dashboard_data:
                summary = self.dashboard_data['portfolio_summary']
                logger.info(f"Portfolio update: Balance: ${summary['current_balance']:,.2f}, "
                           f"P&L: ${summary['total_pnl']:+,.2f}, "
                           f"Positions: {summary['open_positions']}")
            
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
            self.dashboard_data = {'error': str(e)}
    
    def run_scheduler(self):
        """Run the background scheduler"""
        # Schedule portfolio updates based on timeframe
        update_interval = self.timeframe_config['update_interval']
        schedule.every(update_interval).seconds.do(self.update_portfolio_job)
        
        # Initial data load
        logger.info("Loading initial portfolio data...")
        self.update_portfolio_job()
        
        while self.running:
            schedule.run_pending()
            time.sleep(1)
    
    def start(self):
        """Start the trading dashboard"""
        self.running = True
        
        # Start background scheduler
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info(f"Starting trading dashboard on port {self.port}")
        logger.info(f"Dashboard will be available at: http://localhost:{self.port}")
        
        self.app.run(host='0.0.0.0', port=self.port, debug=False, threaded=True)
    
    def stop(self):
        """Stop the trading dashboard"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
    
    def print_console_dashboard(self):
        """Print a console/terminal dashboard"""
        try:
            if not self.dashboard_data or 'portfolio_summary' not in self.dashboard_data:
                print("No portfolio data available")
                return
            
            console_data = self._format_for_console(self.dashboard_data)
            
            # Print header
            print("\n" + "="*80)
            print(f"CRYPTOCURRENCY PAPER TRADING BOT - {console_data.get('timeframe', 'Unknown')} TIMEFRAME")
            print("="*80)
            
            # Print portfolio summary
            summary = console_data['summary']
            print(f"\nPORTFOLIO SUMMARY:")
            print(f"  Balance: {summary['balance']} | Total Value: {summary['total_value']}")
            print(f"  Total P&L: {summary['total_pnl']} | Daily P&L: {summary['daily_pnl']}")
            print(f"  Unrealized P&L: {summary['unrealized_pnl']} | Allocation: {summary['allocation']}")
            print(f"  Win Rate: {summary['win_rate']} | Max Drawdown: {summary['max_drawdown']}")
            print(f"  Open Positions: {summary['open_positions']} | Total Trades: {summary['total_trades']}")
            
            # Print open positions
            positions = console_data['positions']
            if positions:
                print(f"\nOPEN POSITIONS ({len(positions)}):")
                print(f"{'Symbol':<12} {'Type':<6} {'Entry':<12} {'Current':<12} {'Stop Loss':<12} {'P&L':<12} {'Pattern':<20}")
                print("-" * 100)
                for pos in positions:
                    print(f"{pos['symbol']:<12} {pos['type']:<6} {pos['entry']:<12} {pos['current']:<12} "
                          f"{pos['stop_loss']:<12} {pos['pnl']:<12} {pos['pattern']:<20}")
            else:
                print("\nOPEN POSITIONS: None")
            
            # Print recent trades
            trades = console_data['recent_trades']
            if trades:
                print(f"\nRECENT TRADES ({len(trades)}):")
                print(f"{'Symbol':<12} {'Type':<6} {'P&L':<12} {'R:R':<8} {'Time':<8} {'Exit Reason':<20}")
                print("-" * 80)
                for trade in trades:
                    print(f"{trade['symbol']:<12} {trade['type']:<6} {trade['pnl']:<12} "
                          f"{trade['rr_ratio']:<8} {trade['hold_time']:<8} {trade['exit_reason']:<20}")
            else:
                print("\nRECENT TRADES: None")
            
            print(f"\nLast Update: {console_data.get('last_update', 'Unknown')}")
            print("="*80)
            
        except Exception as e:
            print(f"Error printing console dashboard: {e}")
